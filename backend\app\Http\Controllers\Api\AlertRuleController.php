<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\AlertRule;
use App\Models\MonitoringTask;
use App\Models\TaskGroup;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;

class AlertRuleController extends Controller
{
    /**
     * 获取预警规则列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = AlertRule::with(['user', 'monitoringTask', 'alerts'])
            ->where('user_id', Auth::id());

        // 筛选条件
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('rule_type')) {
            $query->where('rule_type', $request->rule_type);
        }

        if ($request->has('monitoring_task_id')) {
            $query->where('monitoring_task_id', $request->monitoring_task_id);
        }

        if ($request->has('task_group_id')) {
            $query->where('task_group_id', $request->task_group_id);
        }

        if ($request->has('severity')) {
            $query->where('severity', $request->severity);
        }

        // 搜索
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('target_field', 'like', "%{$search}%");
            });
        }

        // 排序
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // 分页
        $perPage = $request->get('per_page', 15);
        $rules = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $rules,
            'message' => '预警规则列表获取成功'
        ]);
    }

    /**
     * 创建预警规则
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'rule_type' => ['required', Rule::in(AlertRule::getRuleTypes())],
            'target_field' => 'required|string|max:255',
            'operator' => ['required', Rule::in(AlertRule::getOperators())],
            'threshold' => 'required|string|max:255',
            'severity' => ['required', Rule::in(AlertRule::getSeverityLevels())],
            'monitoring_task_id' => 'nullable|exists:monitoring_tasks,id',
            'task_group_id' => 'nullable|exists:task_groups,id',
            'notification_channels' => 'nullable|array',
            'notification_channels.*' => 'string|in:email,sms,webhook,database',
            'recipients' => 'nullable|array',
            'recipients.*' => 'string|email',
            'webhook_url' => 'nullable|url',
            'cooldown_minutes' => 'nullable|integer|min:1|max:1440',
            'max_alerts_per_hour' => 'nullable|integer|min:1|max:100',
            'status' => 'nullable|in:active,inactive'
        ]);

        // 验证监控任务是否属于当前用户
        if (!empty($validated['monitoring_task_id'])) {
            $task = MonitoringTask::where('id', $validated['monitoring_task_id'])
                ->where('user_id', Auth::id())
                ->first();
            
            if (!$task) {
                return response()->json([
                    'success' => false,
                    'message' => '指定的监控任务不存在或无权访问'
                ], 403);
            }
        }

        // 验证任务组是否属于当前用户
        if (!empty($validated['task_group_id'])) {
            $taskGroup = TaskGroup::where('id', $validated['task_group_id'])
                ->where('user_id', Auth::id())
                ->first();
            
            if (!$taskGroup) {
                return response()->json([
                    'success' => false,
                    'message' => '指定的任务组不存在或无权访问'
                ], 403);
            }
        }

        // 设置默认值
        $validated['user_id'] = Auth::id();
        $validated['status'] = $validated['status'] ?? 'active';
        $validated['cooldown_minutes'] = $validated['cooldown_minutes'] ?? 60;
        $validated['max_alerts_per_hour'] = $validated['max_alerts_per_hour'] ?? 10;

        // 处理JSON字段
        if (isset($validated['notification_channels'])) {
            $validated['notification_channels'] = json_encode($validated['notification_channels']);
        }
        if (isset($validated['recipients'])) {
            $validated['recipients'] = json_encode($validated['recipients']);
        }

        $alertRule = AlertRule::create($validated);
        $alertRule->load(['user', 'monitoringTask']);

        return response()->json([
            'success' => true,
            'data' => $alertRule,
            'message' => '预警规则创建成功'
        ], 201);
    }

    /**
     * 获取单个预警规则详情
     */
    public function show(AlertRule $alertRule): JsonResponse
    {
        // 检查权限
        if ($alertRule->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权访问此预警规则'
            ], 403);
        }

        $alertRule->load(['user', 'monitoringTask', 'alerts' => function ($query) {
            $query->latest()->limit(10);
        }]);

        return response()->json([
            'success' => true,
            'data' => $alertRule,
            'message' => '预警规则详情获取成功'
        ]);
    }

    /**
     * 更新预警规则
     */
    public function update(Request $request, AlertRule $alertRule): JsonResponse
    {
        // 检查权限
        if ($alertRule->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权修改此预警规则'
            ], 403);
        }

        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'rule_type' => ['sometimes', 'required', Rule::in(AlertRule::getRuleTypes())],
            'target_field' => 'sometimes|required|string|max:255',
            'operator' => ['sometimes', 'required', Rule::in(AlertRule::getOperators())],
            'threshold' => 'sometimes|required|string|max:255',
            'severity' => ['sometimes', 'required', Rule::in(AlertRule::getSeverityLevels())],
            'monitoring_task_id' => 'nullable|exists:monitoring_tasks,id',
            'task_group_id' => 'nullable|exists:task_groups,id',
            'notification_channels' => 'nullable|array',
            'notification_channels.*' => 'string|in:email,sms,webhook,database',
            'recipients' => 'nullable|array',
            'recipients.*' => 'string|email',
            'webhook_url' => 'nullable|url',
            'cooldown_minutes' => 'nullable|integer|min:1|max:1440',
            'max_alerts_per_hour' => 'nullable|integer|min:1|max:100',
            'status' => 'nullable|in:active,inactive'
        ]);

        // 验证监控任务是否属于当前用户
        if (!empty($validated['monitoring_task_id'])) {
            $task = MonitoringTask::where('id', $validated['monitoring_task_id'])
                ->where('user_id', Auth::id())
                ->first();
            
            if (!$task) {
                return response()->json([
                    'success' => false,
                    'message' => '指定的监控任务不存在或无权访问'
                ], 403);
            }
        }

        // 验证任务组是否属于当前用户
        if (!empty($validated['task_group_id'])) {
            $taskGroup = TaskGroup::where('id', $validated['task_group_id'])
                ->where('user_id', Auth::id())
                ->first();
            
            if (!$taskGroup) {
                return response()->json([
                    'success' => false,
                    'message' => '指定的任务组不存在或无权访问'
                ], 403);
            }
        }

        // 处理JSON字段
        if (isset($validated['notification_channels'])) {
            $validated['notification_channels'] = json_encode($validated['notification_channels']);
        }
        if (isset($validated['recipients'])) {
            $validated['recipients'] = json_encode($validated['recipients']);
        }

        $alertRule->update($validated);
        $alertRule->load(['user', 'monitoringTask']);

        return response()->json([
            'success' => true,
            'data' => $alertRule,
            'message' => '预警规则更新成功'
        ]);
    }

    /**
     * 删除预警规则
     */
    public function destroy(AlertRule $alertRule): JsonResponse
    {
        // 检查权限
        if ($alertRule->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权删除此预警规则'
            ], 403);
        }

        $alertRule->delete();

        return response()->json([
            'success' => true,
            'message' => '预警规则删除成功'
        ]);
    }

    /**
     * 批量删除预警规则
     */
    public function batchDestroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => 'required|array|min:1',
            'ids.*' => 'integer|exists:alert_rules,id'
        ]);

        $deletedCount = AlertRule::whereIn('id', $validated['ids'])
            ->where('user_id', Auth::id())
            ->delete();

        return response()->json([
            'success' => true,
            'data' => ['deleted_count' => $deletedCount],
            'message' => "成功删除 {$deletedCount} 个预警规则"
        ]);
    }

    /**
     * 启用预警规则
     */
    public function activate(AlertRule $alertRule): JsonResponse
    {
        // 检查权限
        if ($alertRule->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权操作此预警规则'
            ], 403);
        }

        $alertRule->update(['status' => 'active']);

        return response()->json([
            'success' => true,
            'data' => $alertRule,
            'message' => '预警规则已启用'
        ]);
    }

    /**
     * 禁用预警规则
     */
    public function deactivate(AlertRule $alertRule): JsonResponse
    {
        // 检查权限
        if ($alertRule->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权操作此预警规则'
            ], 403);
        }

        $alertRule->update(['status' => 'inactive']);

        return response()->json([
            'success' => true,
            'data' => $alertRule,
            'message' => '预警规则已禁用'
        ]);
    }

    /**
     * 批量更新预警规则状态
     */
    public function batchUpdateStatus(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => 'required|array|min:1',
            'ids.*' => 'integer|exists:alert_rules,id',
            'status' => 'required|in:active,inactive'
        ]);

        $updatedCount = AlertRule::whereIn('id', $validated['ids'])
            ->where('user_id', Auth::id())
            ->update(['status' => $validated['status']]);

        $statusText = $validated['status'] === 'active' ? '启用' : '禁用';

        return response()->json([
            'success' => true,
            'data' => ['updated_count' => $updatedCount],
            'message' => "成功{$statusText} {$updatedCount} 个预警规则"
        ]);
    }

    /**
     * 获取预警规则统计信息
     */
    public function statistics(): JsonResponse
    {
        $userId = Auth::id();

        $stats = [
            'total_rules' => AlertRule::where('user_id', $userId)->count(),
            'active_rules' => AlertRule::where('user_id', $userId)->where('status', 'active')->count(),
            'inactive_rules' => AlertRule::where('user_id', $userId)->where('status', 'inactive')->count(),
            'rules_by_type' => AlertRule::where('user_id', $userId)
                ->selectRaw('rule_type, COUNT(*) as count')
                ->groupBy('rule_type')
                ->pluck('count', 'rule_type'),
            'rules_by_severity' => AlertRule::where('user_id', $userId)
                ->selectRaw('severity, COUNT(*) as count')
                ->groupBy('severity')
                ->pluck('count', 'severity'),
            'recent_alerts' => AlertRule::where('user_id', $userId)
                ->whereHas('alerts', function ($query) {
                    $query->where('created_at', '>=', now()->subDays(7));
                })
                ->count()
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => '预警规则统计信息获取成功'
        ]);
    }

    /**
     * 获取可用选项
     */
    public function options(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'rule_types' => AlertRule::getRuleTypes(),
                'operators' => AlertRule::getOperators(),
                'severity_levels' => AlertRule::getSeverityLevels(),
                'notification_channels' => ['email', 'sms', 'webhook', 'database']
            ],
            'message' => '可用选项获取成功'
        ]);
    }
} 