<template>
  <div class="channel-price-task-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">渠道价格监测</h1>
        <p class="page-description">管理价格监测任务</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" size="default">
          <el-icon><Plus /></el-icon>
          <span class="btn-text">新增任务</span>
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stats-card">
        <div class="stats-icon primary">
          <el-icon><FolderOpened /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-number">12</div>
          <div class="stats-label">任务组</div>
        </div>
      </div>
      <div class="stats-card">
        <div class="stats-icon success">
          <el-icon><ShoppingBag /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-number">256</div>
          <div class="stats-label">监控商品</div>
        </div>
      </div>
      <div class="stats-card">
        <div class="stats-icon warning">
          <el-icon><Timer /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-number">8</div>
          <div class="stats-label">运行中</div>
        </div>
      </div>
      <div class="stats-card">
        <div class="stats-icon danger">
          <el-icon><Warning /></el-icon>
        </div>
        <div class="stats-content">
          <div class="stats-number">3</div>
          <div class="stats-label">预警</div>
        </div>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="task-list-section">
      <div class="section-header">
        <h2 class="section-title">任务列表</h2>
        <div class="section-actions">
          <el-input 
            placeholder="搜索任务..." 
            class="search-input"
            prefix-icon="Search"
          />
        </div>
      </div>
      
      <div class="task-cards">
        <div class="task-card" v-for="i in 6" :key="i">
          <div class="task-header">
            <div class="task-status running"></div>
            <h3 class="task-name">任务组 {{ i }}</h3>
            <el-button size="small" type="text">
              <el-icon><Setting /></el-icon>
            </el-button>
          </div>
          <div class="task-content">
            <p class="task-desc">监控商品价格变动，及时发现异常情况</p>
            <div class="task-meta">
              <span class="meta-item">
                <el-icon><Clock /></el-icon>
                每小时
              </span>
              <span class="meta-item">
                <el-icon><ShoppingBag /></el-icon>
                {{ 10 + i * 5 }} 商品
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { 
  Plus, 
  FolderOpened, 
  ShoppingBag, 
  Timer, 
  Warning, 
  Setting,
  Clock
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
</script>

<style scoped>
.channel-price-task-management {
  padding: 16px;
  background: #f8f9fa;
  min-height: 100%;
  box-sizing: border-box;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 4px 0;
}

.page-description {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stats-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stats-icon.primary {
  background: linear-gradient(135deg, #409EFF, #66b3ff);
}

.stats-icon.success {
  background: linear-gradient(135deg, #67C23A, #85ce61);
}

.stats-icon.warning {
  background: linear-gradient(135deg, #E6A23C, #ebb563);
}

.stats-icon.danger {
  background: linear-gradient(135deg, #F56C6C, #f78989);
}

.stats-content {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1;
}

.stats-label {
  font-size: 12px;
  color: #7f8c8d;
  margin-top: 4px;
}

.task-list-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.search-input {
  width: 250px;
}

.task-cards {
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.task-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.task-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.task-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.task-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.task-status.running {
  background: #67C23A;
}

.task-name {
  flex: 1;
  font-size: 16px;
  font-weight: 500;
  color: #2c3e50;
  margin: 0;
}

.task-desc {
  font-size: 14px;
  color: #606266;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.task-meta {
  display: flex;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .channel-price-task-management {
    padding: 12px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    padding: 16px;
  }
  
  .header-actions {
    justify-content: flex-end;
  }
  
  .btn-text {
    display: none;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .stats-card {
    padding: 16px;
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .stats-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
  
  .stats-number {
    font-size: 20px;
  }
  
  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    padding: 16px;
  }
  
  .search-input {
    width: 100%;
  }
  
  .task-cards {
    grid-template-columns: 1fr;
    padding: 16px;
    gap: 12px;
  }
  
  .task-card {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .channel-price-task-management {
    padding: 8px;
  }
  
  .page-header {
    padding: 12px;
  }
  
  .page-title {
    font-size: 18px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .stats-card {
    padding: 12px;
    flex-direction: row;
    gap: 12px;
  }
  
  .stats-icon {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
  
  .task-cards {
    padding: 12px;
  }
}
</style> 