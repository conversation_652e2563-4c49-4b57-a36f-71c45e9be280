<?php

namespace App\Console\Commands;

use App\Models\MonitoringTask;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ManageMonitoringTasks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'monitoring:manage
                            {action : 操作类型 (list|start|stop|pause|resume|status)}
                            {--id= : 任务ID（针对单个任务操作）}
                            {--all : 应用于所有任务}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '管理监控任务的状态和运行';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $action = $this->argument('action');
        $taskId = $this->option('id');
        $all = $this->option('all');

        try {
            switch ($action) {
                case 'list':
                    return $this->listTasks();
                case 'start':
                    return $this->startTasks($taskId, $all);
                case 'stop':
                    return $this->stopTasks($taskId, $all);
                case 'pause':
                    return $this->pauseTasks($taskId, $all);
                case 'resume':
                    return $this->resumeTasks($taskId, $all);
                case 'status':
                    return $this->showStatus($taskId);
                default:
                    $this->error("未知操作: {$action}");
                    return self::FAILURE;
            }
        } catch (\Exception $e) {
            $this->error("操作失败: {$e->getMessage()}");
            return self::FAILURE;
        }
    }

    /**
     * 列出所有监控任务
     */
    private function listTasks(): int
    {
        $tasks = MonitoringTask::with(['user', 'dataSource'])
            ->orderBy('created_at', 'desc')
            ->get();

        if ($tasks->isEmpty()) {
            $this->info('没有找到监控任务。');
            return self::SUCCESS;
        }

        $headers = ['ID', '任务名称', '状态', '数据源', '创建者', '运行次数', '成功率', '下次运行'];
        $rows = [];

        foreach ($tasks as $task) {
            $rows[] = [
                $task->id,
                $task->name,
                $this->getStatusDisplay($task->status),
                $task->dataSource->name ?? 'N/A',
                $task->user->name ?? 'N/A',
                $task->run_count,
                $task->run_count > 0 ? round(($task->success_count / $task->run_count) * 100, 1) . '%' : 'N/A',
                $task->next_run_at ? $task->next_run_at->format('Y-m-d H:i:s') : 'N/A',
            ];
        }

        $this->table($headers, $rows);
        return self::SUCCESS;
    }

    /**
     * 启动任务
     */
    private function startTasks(?string $taskId, bool $all): int
    {
        $tasks = $this->getTasks($taskId, $all);
        
        if ($tasks->isEmpty()) {
            $this->error('没有找到要启动的任务。');
            return self::FAILURE;
        }

        $updated = 0;
        foreach ($tasks as $task) {
            if ($task->status !== 'running') {
                $task->update([
                    'status' => 'running',
                    'next_run_at' => now()->addMinutes($task->frequency_value ?? 30),
                ]);
                $updated++;
                $this->line("✓ 已启动任务: {$task->name} (ID: {$task->id})");
            } else {
                $this->line("- 任务已在运行: {$task->name} (ID: {$task->id})");
            }
        }

        $this->info("成功启动 {$updated} 个任务。");
        return self::SUCCESS;
    }

    /**
     * 停止任务
     */
    private function stopTasks(?string $taskId, bool $all): int
    {
        $tasks = $this->getTasks($taskId, $all);
        
        if ($tasks->isEmpty()) {
            $this->error('没有找到要停止的任务。');
            return self::FAILURE;
        }

        $updated = 0;
        foreach ($tasks as $task) {
            if ($task->status !== 'stopped') {
                $task->update([
                    'status' => 'stopped',
                    'next_run_at' => null,
                ]);
                $updated++;
                $this->line("✓ 已停止任务: {$task->name} (ID: {$task->id})");
            } else {
                $this->line("- 任务已停止: {$task->name} (ID: {$task->id})");
            }
        }

        $this->info("成功停止 {$updated} 个任务。");
        return self::SUCCESS;
    }

    /**
     * 暂停任务
     */
    private function pauseTasks(?string $taskId, bool $all): int
    {
        $tasks = $this->getTasks($taskId, $all);
        
        if ($tasks->isEmpty()) {
            $this->error('没有找到要暂停的任务。');
            return self::FAILURE;
        }

        $updated = 0;
        foreach ($tasks as $task) {
            if ($task->status === 'running') {
                $task->update(['status' => 'paused']);
                $updated++;
                $this->line("✓ 已暂停任务: {$task->name} (ID: {$task->id})");
            } else {
                $this->line("- 任务不在运行状态: {$task->name} (ID: {$task->id})");
            }
        }

        $this->info("成功暂停 {$updated} 个任务。");
        return self::SUCCESS;
    }

    /**
     * 恢复任务
     */
    private function resumeTasks(?string $taskId, bool $all): int
    {
        $tasks = $this->getTasks($taskId, $all);
        
        if ($tasks->isEmpty()) {
            $this->error('没有找到要恢复的任务。');
            return self::FAILURE;
        }

        $updated = 0;
        foreach ($tasks as $task) {
            if ($task->status === 'paused') {
                $task->update([
                    'status' => 'running',
                    'next_run_at' => now()->addMinutes($task->frequency_value ?? 30),
                ]);
                $updated++;
                $this->line("✓ 已恢复任务: {$task->name} (ID: {$task->id})");
            } else {
                $this->line("- 任务不在暂停状态: {$task->name} (ID: {$task->id})");
            }
        }

        $this->info("成功恢复 {$updated} 个任务。");
        return self::SUCCESS;
    }

    /**
     * 显示任务状态
     */
    private function showStatus(?string $taskId): int
    {
        if (!$taskId) {
            // 显示系统整体状态
            $this->showSystemStatus();
        } else {
            // 显示单个任务状态
            $this->showTaskStatus($taskId);
        }

        return self::SUCCESS;
    }

    /**
     * 显示系统整体状态
     */
    private function showSystemStatus(): void
    {
        $this->info('=== 监控任务系统状态 ===');
        
        $stats = MonitoringTask::selectRaw('
            status,
            COUNT(*) as count,
            SUM(run_count) as total_runs,
            SUM(success_count) as total_success,
            SUM(failed_count) as total_failed
        ')->groupBy('status')->get();

        $headers = ['状态', '任务数', '总运行次数', '成功次数', '失败次数', '成功率'];
        $rows = [];

        foreach ($stats as $stat) {
            $successRate = $stat->total_runs > 0 
                ? round(($stat->total_success / $stat->total_runs) * 100, 1) . '%'
                : 'N/A';

            $rows[] = [
                $this->getStatusDisplay($stat->status),
                $stat->count,
                $stat->total_runs,
                $stat->total_success,
                $stat->total_failed,
                $successRate,
            ];
        }

        $this->table($headers, $rows);

        // 显示即将执行的任务
        $upcomingTasks = MonitoringTask::where('status', 'running')
            ->whereNotNull('next_run_at')
            ->where('next_run_at', '<=', now()->addHours(1))
            ->orderBy('next_run_at')
            ->limit(5)
            ->get();

        if ($upcomingTasks->isNotEmpty()) {
            $this->info("\n=== 即将执行的任务（1小时内）===");
            $headers = ['ID', '任务名称', '下次运行时间', '剩余时间'];
            $rows = [];

            foreach ($upcomingTasks as $task) {
                $remainingTime = now()->diffInMinutes($task->next_run_at, false);
                $remainingDisplay = $remainingTime > 0 
                    ? "{$remainingTime} 分钟后"
                    : "已到期";

                $rows[] = [
                    $task->id,
                    $task->name,
                    $task->next_run_at->format('Y-m-d H:i:s'),
                    $remainingDisplay,
                ];
            }

            $this->table($headers, $rows);
        }
    }

    /**
     * 显示单个任务状态
     */
    private function showTaskStatus(string $taskId): void
    {
        $task = MonitoringTask::with(['user', 'dataSource'])->find($taskId);
        
        if (!$task) {
            $this->error("任务不存在: ID {$taskId}");
            return;
        }

        $this->info("=== 任务详细状态 ===");
        $this->line("任务ID: {$task->id}");
        $this->line("任务名称: {$task->name}");
        $this->line("描述: {$task->description}");
        $this->line("状态: " . $this->getStatusDisplay($task->status));
        $this->line("数据源: " . ($task->dataSource->name ?? 'N/A'));
        $this->line("创建者: " . ($task->user->name ?? 'N/A'));
        $this->line("频率类型: {$task->frequency_type}");
        $this->line("频率值: {$task->frequency_value}");
        $this->line("Cron表达式: " . ($task->cron_expression ?? 'N/A'));
        $this->line("自动启动: " . ($task->auto_start ? '是' : '否'));
        $this->line("目标产品数: " . count($task->target_products ?? []));
        $this->line("监控字段: " . implode(', ', $task->monitor_fields ?? []));
        $this->line("运行次数: {$task->run_count}");
        $this->line("成功次数: {$task->success_count}");
        $this->line("失败次数: {$task->failed_count}");
        
        if ($task->run_count > 0) {
            $successRate = round(($task->success_count / $task->run_count) * 100, 1);
            $this->line("成功率: {$successRate}%");
        }
        
        $this->line("上次运行: " . ($task->last_run_at ? $task->last_run_at->format('Y-m-d H:i:s') : '从未运行'));
        $this->line("下次运行: " . ($task->next_run_at ? $task->next_run_at->format('Y-m-d H:i:s') : '未设置'));
        
        if ($task->last_error) {
            $this->line("最后错误: {$task->last_error}");
        }
    }

    /**
     * 获取任务
     */
    private function getTasks(?string $taskId, bool $all): \Illuminate\Database\Eloquent\Collection
    {
        if ($taskId) {
            $task = MonitoringTask::find($taskId);
            return $task ? collect([$task]) : collect();
        }
        
        if ($all) {
            return MonitoringTask::all();
        }
        
        return collect();
    }

    /**
     * 获取状态显示文本
     */
    private function getStatusDisplay(string $status): string
    {
        $statusMap = [
            'pending' => '等待中',
            'running' => '运行中',
            'paused' => '已暂停',
            'stopped' => '已停止',
            'failed' => '失败',
        ];

        return $statusMap[$status] ?? $status;
    }
} 