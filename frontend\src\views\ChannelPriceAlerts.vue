<template>
  <div class="channel-price-alerts">
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">渠道价格监测 - 预警中心</h1>
        <p class="page-description">设置预警规则并查看已触发的预警历史，及时发现价格异动</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" :icon="Plus" @click="showCreateRuleDialog = true">
          创建预警规则
        </el-button>
      </div>
    </div>

    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon active">
                <el-icon><Bell /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.activeRules }}</div>
                <div class="stat-label">活跃规则</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon today">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.todayAlerts }}</div>
                <div class="stat-label">今日预警</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon unread">
                <el-icon><Message /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.unreadAlerts }}</div>
                <div class="stat-label">未读预警</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.totalAlerts }}</div>
                <div class="stat-label">总预警数</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <el-tabs v-model="activeTab" class="alert-tabs">
      <!-- 预警规则 -->
      <el-tab-pane label="预警规则" name="rules">
        <el-card class="rules-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">预警规则列表</span>
              <div class="card-actions">
                <el-input
                  v-model="rulesSearchQuery"
                  placeholder="搜索规则..."
                  :prefix-icon="Search"
                  clearable
                  style="width: 300px"
                />
              </div>
            </div>
          </template>

          <el-table
            :data="filteredRules"
            stripe
            style="width: 100%"
            v-loading="rulesLoading"
          >
            <el-table-column prop="name" label="规则名称" min-width="200">
              <template #default="{ row }">
                <div class="rule-name">
                  <el-icon class="rule-icon" :color="row.enabled ? '#67C23A' : '#C0C4CC'">
                    <Bell />
                  </el-icon>
                  <span>{{ row.name }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="taskGroup" label="任务组" min-width="150">
              <template #default="{ row }">
                <el-tag type="info" size="small">{{ row.taskGroup }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="condition" label="触发条件" min-width="200">
              <template #default="{ row }">
                <div class="condition-text">{{ formatCondition(row.condition) }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="notificationMethod" label="通知方式" width="120">
              <template #default="{ row }">
                <div class="notification-methods">
                  <el-tag
                    v-for="method in row.notificationMethod"
                    :key="method"
                    size="small"
                    class="method-tag"
                  >
                    {{ getNotificationMethodText(method) }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="enabled" label="状态" width="100">
              <template #default="{ row }">
                <el-switch
                  v-model="row.enabled"
                  @change="toggleRule(row)"
                  active-text="启用"
                  inactive-text="禁用"
                />
              </template>
            </el-table-column>
            <el-table-column prop="lastTriggered" label="最后触发" width="160">
              <template #default="{ row }">
                <span v-if="row.lastTriggered" class="last-triggered">
                  {{ formatTime(row.lastTriggered) }}
                </span>
                <span v-else class="never-triggered">从未触发</span>
              </template>
            </el-table-column>
            <el-table-column prop="createdAt" label="创建时间" width="160">
              <template #default="{ row }">
                <span>{{ formatTime(row.createdAt) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="{ row }">
                <el-button-group>
                  <el-button size="small" :icon="Edit" @click="editRule(row)">
                    编辑
                  </el-button>
                  <el-button size="small" :icon="View" @click="viewRule(row)">
                    详情
                  </el-button>
                  <el-button
                    size="small"
                    :icon="Delete"
                    type="danger"
                    @click="deleteRule(row)"
                  >
                    删除
                  </el-button>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="rulesCurrentPage"
              v-model:page-size="rulesPageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="totalRules"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleRulesPageSizeChange"
              @current-change="handleRulesCurrentChange"
            />
          </div>
        </el-card>
      </el-tab-pane>

      <!-- 预警历史 -->
      <el-tab-pane label="预警历史" name="history">
        <el-card class="history-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">预警历史</span>
              <div class="card-actions">
                <el-select
                  v-model="historyFilter.status"
                  placeholder="状态筛选"
                  clearable
                  style="width: 120px; margin-right: 12px"
                >
                  <el-option label="未读" value="unread" />
                  <el-option label="已读" value="read" />
                  <el-option label="已处理" value="handled" />
                </el-select>
                <el-date-picker
                  v-model="historyFilter.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 240px; margin-right: 12px"
                />
                <el-input
                  v-model="historySearchQuery"
                  placeholder="搜索预警..."
                  :prefix-icon="Search"
                  clearable
                  style="width: 300px"
                />
              </div>
            </div>
          </template>

          <el-table
            :data="filteredAlerts"
            stripe
            style="width: 100%"
            v-loading="historyLoading"
            @row-click="markAlertAsRead"
          >
            <el-table-column prop="status" label="状态" width="80">
              <template #default="{ row }">
                <el-badge
                  :is-dot="row.status === 'unread'"
                  :type="getAlertStatusBadgeType(row.status)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="level" label="级别" width="100">
              <template #default="{ row }">
                <el-tag :type="getAlertLevelType(row.level)" size="small">
                  {{ getAlertLevelText(row.level) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="ruleName" label="预警规则" min-width="150">
              <template #default="{ row }">
                <span class="rule-name-text">{{ row.ruleName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="productInfo" label="商品信息" min-width="300">
              <template #default="{ row }">
                <div class="product-info">
                  <el-image
                    :src="row.productInfo.image"
                    :preview-src-list="[row.productInfo.image]"
                    fit="cover"
                    style="width: 40px; height: 40px; border-radius: 4px; margin-right: 12px"
                  />
                  <div class="product-details">
                    <div class="product-title">{{ row.productInfo.title }}</div>
                    <div class="product-meta">
                      <span class="product-id">ID: {{ row.productInfo.id }}</span>
                      <span class="product-price">当前价格: ¥{{ row.productInfo.currentPrice }}</span>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="message" label="预警消息" min-width="250">
              <template #default="{ row }">
                <div class="alert-message">{{ row.message }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="triggeredAt" label="触发时间" width="160">
              <template #default="{ row }">
                <span>{{ formatTime(row.triggeredAt) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row }">
                <el-button-group>
                  <el-button
                    size="small"
                    :icon="View"
                    @click="viewAlertDetail(row)"
                    type="primary"
                    link
                  >
                    详情
                  </el-button>
                  <el-button
                    v-if="row.status !== 'handled'"
                    size="small"
                    :icon="Check"
                    @click="markAsHandled(row)"
                    type="success"
                    link
                  >
                    标记处理
                  </el-button>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="historyCurrentPage"
              v-model:page-size="historyPageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="totalAlerts"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleHistoryPageSizeChange"
              @current-change="handleHistoryCurrentChange"
            />
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建预警规则对话框 -->
    <el-dialog
      v-model="showCreateRuleDialog"
      title="创建预警规则"
      width="800px"
      :before-close="handleRuleDialogClose"
    >
      <el-form
        ref="ruleFormRef"
        :model="ruleForm"
        :rules="ruleFormRules"
        label-width="120px"
      >
        <el-form-item label="规则名称" prop="name">
          <el-input
            v-model="ruleForm.name"
            placeholder="请输入预警规则名称"
          />
        </el-form-item>
        <el-form-item label="应用范围" prop="scope">
          <el-radio-group v-model="ruleForm.scope">
            <el-radio label="taskGroup">任务组</el-radio>
            <el-radio label="product">特定商品</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="ruleForm.scope === 'taskGroup'" label="选择任务组" prop="taskGroupId">
          <el-select
            v-model="ruleForm.taskGroupId"
            placeholder="请选择任务组"
            style="width: 100%"
          >
            <el-option
              v-for="group in taskGroups"
              :key="group.id"
              :label="group.name"
              :value="group.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="ruleForm.scope === 'product'" label="商品ID" prop="productIds">
          <el-input
            v-model="ruleForm.productIds"
            type="textarea"
            :rows="3"
            placeholder="请输入商品ID，多个ID用换行分隔"
          />
        </el-form-item>
        <el-form-item label="预警条件" prop="conditionType">
          <el-select
            v-model="ruleForm.conditionType"
            placeholder="请选择预警条件类型"
            style="width: 100%"
            @change="handleConditionTypeChange"
          >
            <el-option label="促销价偏离率" value="promo_deviation" />
            <el-option label="渠道价格偏离率" value="channel_deviation" />
            <el-option label="商品状态变化" value="status_change" />
            <el-option label="库存变化" value="stock_change" />
            <el-option label="数据更新超时" value="data_timeout" />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="['promo_deviation', 'channel_deviation'].includes(ruleForm.conditionType)"
          label="阈值设置"
          prop="threshold"
        >
          <div class="threshold-setting">
            <el-select v-model="ruleForm.thresholdOperator" style="width: 100px">
              <el-option label=">" value="gt" />
              <el-option label=">=" value="gte" />
              <el-option label="<" value="lt" />
              <el-option label="<=" value="lte" />
            </el-select>
            <el-input-number
              v-model="ruleForm.threshold"
              :min="0"
              :max="100"
              :precision="1"
              style="width: 150px; margin: 0 8px"
            />
            <span>%</span>
          </div>
        </el-form-item>
        <el-form-item
          v-if="ruleForm.conditionType === 'status_change'"
          label="状态变化"
          prop="statusChange"
        >
          <el-checkbox-group v-model="ruleForm.statusChange">
            <el-checkbox label="online_to_offline">上架变下架</el-checkbox>
            <el-checkbox label="offline_to_online">下架变上架</el-checkbox>
            <el-checkbox label="price_change">价格变动</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item
          v-if="ruleForm.conditionType === 'data_timeout'"
          label="超时时间"
          prop="timeoutHours"
        >
          <el-input-number
            v-model="ruleForm.timeoutHours"
            :min="1"
            :max="168"
            style="width: 150px"
          />
          <span style="margin-left: 8px">小时</span>
        </el-form-item>
        <el-form-item label="预警级别" prop="level">
          <el-select v-model="ruleForm.level" placeholder="请选择预警级别">
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="critical" />
          </el-select>
        </el-form-item>
        <el-form-item label="通知方式" prop="notificationMethod">
          <el-checkbox-group v-model="ruleForm.notificationMethod">
            <el-checkbox label="system">站内消息</el-checkbox>
            <el-checkbox label="email">邮件通知</el-checkbox>
            <el-checkbox label="sms">短信通知</el-checkbox>
            <el-checkbox label="webhook">Webhook</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="通知人员" prop="notificationUsers">
          <el-select
            v-model="ruleForm.notificationUsers"
            multiple
            placeholder="请选择通知人员"
            style="width: 100%"
          >
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.name"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="ruleForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入规则描述（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showCreateRuleDialog = false">取消</el-button>
          <el-button type="primary" @click="createRule" :loading="creatingRule">
            创建规则
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Bell,
  Warning,
  Message,
  DataAnalysis,
  Search,
  Edit,
  View,
  Delete,
  Check
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('rules')
const rulesLoading = ref(false)
const historyLoading = ref(false)
const showCreateRuleDialog = ref(false)
const creatingRule = ref(false)

// 搜索和筛选
const rulesSearchQuery = ref('')
const historySearchQuery = ref('')
const historyFilter = reactive({
  status: '',
  dateRange: [] as Date[]
})

// 分页
const rulesCurrentPage = ref(1)
const rulesPageSize = ref(20)
const totalRules = ref(0)
const historyCurrentPage = ref(1)
const historyPageSize = ref(20)
const totalAlerts = ref(0)

// 统计数据
const stats = reactive({
  activeRules: 15,
  todayAlerts: 8,
  unreadAlerts: 3,
  totalAlerts: 156
})

// 预警规则表单
const ruleForm = reactive({
  name: '',
  scope: 'taskGroup',
  taskGroupId: '',
  productIds: '',
  conditionType: '',
  thresholdOperator: 'gt',
  threshold: 10,
  statusChange: [] as string[],
  timeoutHours: 24,
  level: 'medium',
  notificationMethod: ['system'] as string[],
  notificationUsers: [] as number[],
  description: ''
})

const ruleFormRules = {
  name: [
    { required: true, message: '请输入规则名称', trigger: 'blur' }
  ],
  scope: [
    { required: true, message: '请选择应用范围', trigger: 'change' }
  ],
  taskGroupId: [
    { required: true, message: '请选择任务组', trigger: 'change' }
  ],
  conditionType: [
    { required: true, message: '请选择预警条件', trigger: 'change' }
  ],
  level: [
    { required: true, message: '请选择预警级别', trigger: 'change' }
  ],
  notificationMethod: [
    { required: true, message: '请选择通知方式', trigger: 'change' }
  ]
}

// 任务组列表
const taskGroups = ref([
  { id: 1, name: '品牌A官方旗舰店监控' },
  { id: 2, name: '品牌B竞品价格监控' },
  { id: 3, name: '热销商品价格跟踪' }
])

// 用户列表
const users = ref([
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' }
])

// 预警规则列表
const rules = ref([
  {
    id: 1,
    name: '促销价偏离预警',
    taskGroup: '品牌A官方旗舰店监控',
    condition: {
      type: 'promo_deviation',
      operator: 'gt',
      value: 15
    },
    notificationMethod: ['system', 'email'],
    enabled: true,
    lastTriggered: new Date(Date.now() - 2 * 60 * 60 * 1000),
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
  },
  {
    id: 2,
    name: '商品下架预警',
    taskGroup: '品牌B竞品价格监控',
    condition: {
      type: 'status_change',
      value: ['online_to_offline']
    },
    notificationMethod: ['system', 'sms'],
    enabled: true,
    lastTriggered: null,
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
  }
])

// 预警历史列表
const alerts = ref([
  {
    id: 1,
    ruleName: '促销价偏离预警',
    level: 'high',
    status: 'unread',
    productInfo: {
      id: 'TB123456789',
      title: 'iPhone 15 Pro Max 256GB 深空黑色',
      image: 'https://via.placeholder.com/40',
      currentPrice: 8999
    },
    message: '商品促销价偏离率达到 18.5%，超过设定阈值 15%',
    triggeredAt: new Date(Date.now() - 30 * 60 * 1000)
  },
  {
    id: 2,
    ruleName: '商品下架预警',
    level: 'medium',
    status: 'read',
    productInfo: {
      id: 'JD987654321',
      title: '华为 Mate 60 Pro 12GB+512GB',
      image: 'https://via.placeholder.com/40',
      currentPrice: 0
    },
    message: '商品状态从上架变为下架',
    triggeredAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
  }
])

// 计算属性
const filteredRules = computed(() => {
  if (!rulesSearchQuery.value) return rules.value
  return rules.value.filter(rule =>
    rule.name.toLowerCase().includes(rulesSearchQuery.value.toLowerCase()) ||
    rule.taskGroup.toLowerCase().includes(rulesSearchQuery.value.toLowerCase())
  )
})

const filteredAlerts = computed(() => {
  let filtered = alerts.value

  // 状态筛选
  if (historyFilter.status) {
    filtered = filtered.filter(alert => alert.status === historyFilter.status)
  }

  // 日期范围筛选
  if (historyFilter.dateRange && historyFilter.dateRange.length === 2) {
    const [startDate, endDate] = historyFilter.dateRange
    filtered = filtered.filter(alert => {
      const alertDate = new Date(alert.triggeredAt)
      return alertDate >= startDate && alertDate <= endDate
    })
  }

  // 搜索筛选
  if (historySearchQuery.value) {
    const query = historySearchQuery.value.toLowerCase()
    filtered = filtered.filter(alert =>
      alert.ruleName.toLowerCase().includes(query) ||
      alert.productInfo.title.toLowerCase().includes(query) ||
      alert.message.toLowerCase().includes(query)
    )
  }

  return filtered
})

// 方法
const formatCondition = (condition: any) => {
  switch (condition.type) {
    case 'promo_deviation':
      return `促销价偏离率 ${condition.operator === 'gt' ? '>' : condition.operator === 'gte' ? '>=' : condition.operator === 'lt' ? '<' : '<='} ${condition.value}%`
    case 'channel_deviation':
      return `渠道价格偏离率 ${condition.operator === 'gt' ? '>' : condition.operator === 'gte' ? '>=' : condition.operator === 'lt' ? '<' : '<='} ${condition.value}%`
    case 'status_change':
      return `状态变化: ${condition.value.map((v: string) => {
        switch (v) {
          case 'online_to_offline': return '上架变下架'
          case 'offline_to_online': return '下架变上架'
          case 'price_change': return '价格变动'
          default: return v
        }
      }).join(', ')}`
    case 'data_timeout':
      return `数据更新超时 > ${condition.value}小时`
    default:
      return '未知条件'
  }
}

const getNotificationMethodText = (method: string) => {
  const texts = {
    system: '站内',
    email: '邮件',
    sms: '短信',
    webhook: 'Webhook'
  }
  return texts[method] || method
}

const getAlertLevelType = (level: string) => {
  const types = {
    low: 'info',
    medium: 'warning',
    high: 'danger',
    critical: 'danger'
  }
  return types[level] || 'info'
}

const getAlertLevelText = (level: string) => {
  const texts = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '紧急'
  }
  return texts[level] || level
}

const getAlertStatusBadgeType = (status: string) => {
  const types = {
    unread: 'danger',
    read: 'warning',
    handled: 'success'
  }
  return types[status] || 'info'
}

const formatTime = (date: Date) => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const toggleRule = async (rule: any) => {
  try {
    // TODO: 调用API
    ElMessage.success(`规则已${rule.enabled ? '启用' : '禁用'}`)
  } catch (error) {
    ElMessage.error('操作失败')
    rule.enabled = !rule.enabled // 回滚状态
  }
}

const editRule = (rule: any) => {
  ElMessage.info('编辑功能开发中...')
}

const viewRule = (rule: any) => {
  ElMessage.info('查看详情功能开发中...')
}

const deleteRule = async (rule: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除规则"${rule.name}"吗？此操作不可恢复。`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // TODO: 调用API
    const index = rules.value.findIndex(r => r.id === rule.id)
    if (index > -1) {
      rules.value.splice(index, 1)
    }
    
    ElMessage.success('规则已删除')
  } catch (error) {
    // 用户取消
  }
}

const markAlertAsRead = (alert: any) => {
  if (alert.status === 'unread') {
    alert.status = 'read'
    stats.unreadAlerts--
    // TODO: 调用API标记为已读
  }
}

const viewAlertDetail = (alert: any) => {
  ElMessage.info('查看详情功能开发中...')
}

const markAsHandled = async (alert: any) => {
  try {
    alert.status = 'handled'
    if (stats.unreadAlerts > 0) {
      stats.unreadAlerts--
    }
    // TODO: 调用API标记为已处理
    ElMessage.success('已标记为处理')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const handleConditionTypeChange = () => {
  // 重置相关字段
  ruleForm.threshold = 10
  ruleForm.statusChange = []
  ruleForm.timeoutHours = 24
}

const createRule = async () => {
  // TODO: 表单验证和API调用
  creatingRule.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('预警规则创建成功')
    showCreateRuleDialog.value = false
    resetRuleForm()
    
    // 刷新规则列表
    // TODO: 调用API获取最新数据
  } catch (error) {
    ElMessage.error('规则创建失败')
  } finally {
    creatingRule.value = false
  }
}

const resetRuleForm = () => {
  Object.assign(ruleForm, {
    name: '',
    scope: 'taskGroup',
    taskGroupId: '',
    productIds: '',
    conditionType: '',
    thresholdOperator: 'gt',
    threshold: 10,
    statusChange: [],
    timeoutHours: 24,
    level: 'medium',
    notificationMethod: ['system'],
    notificationUsers: [],
    description: ''
  })
}

const handleRuleDialogClose = (done: () => void) => {
  ElMessageBox.confirm('确定要关闭吗？未保存的数据将丢失。', '确认关闭', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    resetRuleForm()
    done()
  }).catch(() => {
    // 用户取消
  })
}

// 分页处理
const handleRulesPageSizeChange = (size: number) => {
  rulesPageSize.value = size
  // TODO: 重新加载数据
}

const handleRulesCurrentChange = (page: number) => {
  rulesCurrentPage.value = page
  // TODO: 重新加载数据
}

const handleHistoryPageSizeChange = (size: number) => {
  historyPageSize.value = size
  // TODO: 重新加载数据
}

const handleHistoryCurrentChange = (page: number) => {
  historyCurrentPage.value = page
  // TODO: 重新加载数据
}

// 生命周期
onMounted(() => {
  // TODO: 加载数据
  totalRules.value = rules.value.length
  totalAlerts.value = alerts.value.length
})
</script>

<style scoped lang="scss">
.channel-price-alerts {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding: 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-content {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 8px 0;
      }

      .page-description {
        color: #606266;
        margin: 0;
        font-size: 14px;
      }
    }
  }

  .stats-cards {
    margin-bottom: 24px;

    .stat-card {
      .stat-content {
        display: flex;
        align-items: center;
        gap: 16px;

        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          color: white;

          &.active {
            background: linear-gradient(135deg, #67C23A, #85CE61);
          }

          &.today {
            background: linear-gradient(135deg, #E6A23C, #EEBE77);
          }

          &.unread {
            background: linear-gradient(135deg, #F56C6C, #F78989);
          }

          &.total {
            background: linear-gradient(135deg, #409EFF, #66B1FF);
          }
        }

        .stat-info {
          .stat-value {
            font-size: 28px;
            font-weight: 600;
            color: #303133;
            line-height: 1;
          }

          .stat-label {
            font-size: 14px;
            color: #909399;
            margin-top: 4px;
          }
        }
      }
    }
  }

  .alert-tabs {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .card-actions {
        display: flex;
        align-items: center;
      }
    }

    .rule-name {
      display: flex;
      align-items: center;
      gap: 8px;

      .rule-icon {
        font-size: 16px;
      }
    }

    .condition-text {
      font-size: 13px;
      color: #606266;
    }

    .notification-methods {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;

      .method-tag {
        margin: 0;
      }
    }

    .last-triggered {
      color: #909399;
      font-size: 13px;
    }

    .never-triggered {
      color: #C0C4CC;
      font-size: 13px;
      font-style: italic;
    }

    .product-info {
      display: flex;
      align-items: center;

      .product-details {
        flex: 1;

        .product-title {
          font-size: 14px;
          color: #303133;
          margin-bottom: 4px;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .product-meta {
          display: flex;
          gap: 12px;
          font-size: 12px;
          color: #909399;

          .product-price {
            color: #F56C6C;
            font-weight: 500;
          }
        }
      }
    }

    .alert-message {
      font-size: 13px;
      color: #606266;
      line-height: 1.4;
    }

    .rule-name-text {
      font-weight: 500;
      color: #409EFF;
    }

    .pagination-wrapper {
      margin-top: 16px;
      display: flex;
      justify-content: center;
    }
  }

  .threshold-setting {
    display: flex;
    align-items: center;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-table__row) {
  cursor: pointer;
}
</style> 