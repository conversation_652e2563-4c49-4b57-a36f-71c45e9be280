<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DataSource extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'type',
        'description',
        'api_base_url',
        'api_endpoint_template',
        'api_method',
        'api_config',
        'token',
        'headers',
        'default_params',
        'rate_limit',
        'timeout',
        'status',
        'owner_id',
        'last_used_at',
        'total_requests',
        'success_requests',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'headers' => 'array',
        'default_params' => 'array',
        'api_config' => 'array',
        'rate_limit' => 'integer',
        'timeout' => 'integer',
        'status' => 'integer',
        'total_requests' => 'integer',
        'success_requests' => 'integer',
        'last_used_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'token',
    ];

    /**
     * 数据源创建者
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    /**
     * 数据源的字段映射
     */
    public function fieldMappings(): HasMany
    {
        return $this->hasMany(FieldMapping::class);
    }

    /**
     * 数据源的监控任务
     */
    public function monitoringTasks(): HasMany
    {
        return $this->hasMany(MonitoringTask::class);
    }

    /**
     * 获取成功率
     */
    public function getSuccessRateAttribute(): float
    {
        if ($this->total_requests == 0) {
            return 0;
        }
        
        return round(($this->success_requests / $this->total_requests) * 100, 2);
    }

    /**
     * 检查数据源是否可用
     */
    public function isAvailable(): bool
    {
        return $this->status === 1;
    }

    /**
     * 更新使用统计
     */
    public function updateUsageStats(bool $success = true): void
    {
        $this->increment('total_requests');
        
        if ($success) {
            $this->increment('success_requests');
        }
        
        $this->update(['last_used_at' => now()]);
    }

    /**
     * 获取API完整URL
     */
    public function getFullApiUrl(string $endpoint = ''): string
    {
        $baseUrl = rtrim($this->api_base_url, '/');
        $endpoint = ltrim($endpoint, '/');
        
        return $endpoint ? "{$baseUrl}/{$endpoint}" : $baseUrl;
    }

    /**
     * 获取请求头（包含认证信息）
     */
    public function getRequestHeaders(): array
    {
        $headers = $this->headers ?? [];
        
        // 如果有token，添加到请求头
        if ($this->token) {
            $headers['Authorization'] = 'Bearer ' . $this->token;
        }
        
        // 默认请求头
        $defaultHeaders = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
        
        return array_merge($defaultHeaders, $headers);
    }

    /**
     * 获取请求参数（合并默认参数）
     */
    public function getRequestParams(array $customParams = []): array
    {
        $defaultParams = $this->default_params ?? [];
        
        return array_merge($defaultParams, $customParams);
    }

    /**
     * 验证API配置
     */
    public function validateApiConfig(): array
    {
        $errors = [];
        
        if (empty($this->api_base_url)) {
            $errors[] = 'API基础URL不能为空';
        } elseif (!filter_var($this->api_base_url, FILTER_VALIDATE_URL)) {
            $errors[] = 'API基础URL格式不正确';
        }
        
        if ($this->rate_limit <= 0) {
            $errors[] = '频率限制必须大于0';
        }
        
        if ($this->timeout <= 0) {
            $errors[] = '超时时间必须大于0';
        }
        
        return $errors;
    }

    /**
     * 按类型分组获取数据源
     */
    public static function getByType(string $type = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = static::where('status', 1);
        
        if ($type) {
            $query->where('type', $type);
        }
        
        return $query->orderBy('name')->get();
    }

    /**
     * 获取可用的数据源类型
     */
    public static function getAvailableTypes(): array
    {
        return static::where('status', 1)
            ->distinct()
            ->pluck('type')
            ->toArray();
    }
} 