import apiClient from './apiClient';

export const getDataSourceList = (params) => {
  return apiClient.get('/data-sources', { params });
};

export const createDataSource = (data) => {
  return apiClient.post('/data-sources', data);
};

export const updateDataSource = (id, data) => {
  return apiClient.put(`/data-sources/${id}`, data);
};

export const deleteDataSource = (id) => {
  return apiClient.delete(`/data-sources/${id}`);
};

export const testDataSourceConnection = (id) => {
  return apiClient.post(`/data-sources/${id}/test`);
};

export const duplicateDataSource = (id) => {
  return apiClient.post(`/data-sources/${id}/duplicate`);
};

export const getAllDataSources = () => {
  return apiClient.get('/data-sources/all');
}; 