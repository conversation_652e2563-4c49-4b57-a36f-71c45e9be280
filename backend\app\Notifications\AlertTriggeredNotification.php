<?php

namespace App\Notifications;

use App\Models\Alert;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AlertTriggeredNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * 预警实例
     */
    private Alert $alert;

    /**
     * 创建新的通知实例
     */
    public function __construct(Alert $alert)
    {
        $this->alert = $alert;
    }

    /**
     * 获取通知的投递渠道
     */
    public function via($notifiable): array
    {
        $channels = [];
        
        // 从预警规则中获取通知渠道配置
        $notificationChannels = $this->alert->alertRule->notification_channels ?? [];
        
        if (in_array('email', $notificationChannels)) {
            $channels[] = 'mail';
        }
        
        if (in_array('database', $notificationChannels) || in_array('in_app', $notificationChannels)) {
            $channels[] = 'database';
        }
        
        return $channels;
    }

    /**
     * 获取邮件通知的表示形式
     */
    public function toMail($notifiable): MailMessage
    {
        $triggerData = $this->alert->trigger_data ?? [];
        $productTitle = $triggerData['product_data']['standardized_data']['title'] ?? '未知商品';
        $targetValue = $triggerData['target_value'] ?? '未知';
        $targetField = $triggerData['target_field'] ?? '未知字段';
        
        return (new MailMessage)
            ->subject("【预警通知】{$this->alert->title}")
            ->greeting("您好！")
            ->line("您的监控任务触发了一个新的预警。")
            ->line("**预警详情：**")
            ->line("- 预警规则：{$this->alert->title}")
            ->line("- 产品名称：{$productTitle}")
            ->line("- 触发条件：{$targetField} = {$targetValue}")
            ->line("- 预警级别：{$this->alert->alert_level}")
            ->line("- 预警消息：{$this->alert->message}")
            ->line("- 触发时间：{$this->alert->created_at->format('Y-m-d H:i:s')}")
            ->action('查看详情', url("/alerts/{$this->alert->id}"))
            ->line('请及时处理此预警。');
    }

    /**
     * 获取数据库通知的表示形式
     */
    public function toDatabase($notifiable): array
    {
        $triggerData = $this->alert->trigger_data ?? [];
        
        return [
            'alert_id' => $this->alert->id,
            'alert_rule_id' => $this->alert->alert_rule_id,
            'monitoring_task_id' => $this->alert->monitoring_task_id,
            'title' => $this->alert->title,
            'message' => $this->alert->message,
            'alert_level' => $this->alert->alert_level,
            'alert_type' => $this->alert->alert_type,
            'product_info' => [
                'item_id' => $triggerData['product_data']['item_id'] ?? null,
                'title' => $triggerData['product_data']['standardized_data']['title'] ?? '未知商品',
                'data_source' => $triggerData['product_data']['data_source'] ?? '未知数据源',
            ],
            'trigger_info' => [
                'target_field' => $triggerData['target_field'] ?? null,
                'target_value' => $triggerData['target_value'] ?? null,
                'operator' => $triggerData['operator'] ?? null,
                'threshold_values' => $triggerData['threshold_values'] ?? null,
            ],
            'created_at' => $this->alert->created_at->toISOString(),
        ];
    }

    /**
     * 获取通知的数组表示形式
     */
    public function toArray($notifiable): array
    {
        return $this->toDatabase($notifiable);
    }
} 