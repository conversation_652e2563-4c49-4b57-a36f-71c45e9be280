<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\DataSource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;

class DataSourceController extends Controller
{
    /**
     * 获取数据源列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = DataSource::with('owner');
        
        // 支持搜索
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('type', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('api_base_url', 'like', "%{$search}%");
            });
        }
        
        // 按类型筛选
        if ($request->has('type')) {
            $query->where('type', $request->get('type'));
        }
        
        // 支持状态筛选
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }
        
        // 按创建者筛选
        if ($request->has('owner_id')) {
            $query->where('owner_id', $request->get('owner_id'));
        }
        
        // 排序
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);
        
        // 分页
        $perPage = $request->get('per_page', 15);
        $dataSources = $query->paginate($perPage);
        
        return response()->json([
            'success' => true,
            'data' => $dataSources,
            'message' => '数据源列表获取成功'
        ]);
    }
    
    /**
     * 获取数据源类型列表
     */
    public function types(): JsonResponse
    {
        $types = DataSource::getAvailableTypes();
        
        return response()->json([
            'success' => true,
            'data' => $types,
            'message' => '数据源类型列表获取成功'
        ]);
    }
    
    /**
     * 获取单个数据源详情
     */
    public function show(DataSource $dataSource): JsonResponse
    {
        $dataSource->load('owner', 'fieldMappings', 'monitoringTasks');
        
        return response()->json([
            'success' => true,
            'data' => $dataSource,
            'message' => '数据源详情获取成功'
        ]);
    }
    
    /**
     * 创建新数据源
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:100|unique:data_sources,name',
            'type' => 'required|string|max:50',
            'description' => 'nullable|string|max:1000',
            'api_base_url' => 'required|url|max:255',
            'api_config' => 'nullable|array',
            'token' => 'nullable|string|max:255',
            'headers' => 'nullable|array',
            'default_params' => 'nullable|array',
            'rate_limit' => 'integer|min:1|max:10000',
            'timeout' => 'integer|min:1|max:300',
            'status' => 'integer|in:0,1',
        ]);
        
        $dataSource = DataSource::create([
            'name' => $validated['name'],
            'type' => $validated['type'],
            'description' => $validated['description'] ?? '',
            'api_base_url' => $validated['api_base_url'],
            'api_config' => $validated['api_config'] ?? null,
            'token' => $validated['token'] ?? null,
            'headers' => $validated['headers'] ?? null,
            'default_params' => $validated['default_params'] ?? null,
            'rate_limit' => $validated['rate_limit'] ?? 60,
            'timeout' => $validated['timeout'] ?? 30,
            'status' => $validated['status'] ?? 1,
            'owner_id' => Auth::id(),
        ]);
        
        // 验证API配置
        $configErrors = $dataSource->validateApiConfig();
        if (!empty($configErrors)) {
            $dataSource->delete();
            return response()->json([
                'success' => false,
                'message' => 'API配置验证失败',
                'errors' => $configErrors
            ], 422);
        }
        
        $dataSource->load('owner');
        
        return response()->json([
            'success' => true,
            'data' => $dataSource,
            'message' => '数据源创建成功'
        ], 201);
    }
    
    /**
     * 更新数据源
     */
    public function update(Request $request, DataSource $dataSource): JsonResponse
    {
        $validated = $request->validate([
            'name' => [
                'required',
                'string',
                'max:100',
                Rule::unique('data_sources', 'name')->ignore($dataSource->id),
            ],
            'type' => 'required|string|max:50',
            'description' => 'nullable|string|max:1000',
            'api_base_url' => 'required|url|max:255',
            'api_config' => 'nullable|array',
            'token' => 'nullable|string|max:255',
            'headers' => 'nullable|array',
            'default_params' => 'nullable|array',
            'rate_limit' => 'integer|min:1|max:10000',
            'timeout' => 'integer|min:1|max:300',
            'status' => 'integer|in:0,1',
        ]);
        
        $dataSource->update([
            'name' => $validated['name'],
            'type' => $validated['type'],
            'description' => $validated['description'] ?? $dataSource->description,
            'api_base_url' => $validated['api_base_url'],
            'api_config' => $validated['api_config'] ?? $dataSource->api_config,
            'token' => $validated['token'] ?? $dataSource->token,
            'headers' => $validated['headers'] ?? $dataSource->headers,
            'default_params' => $validated['default_params'] ?? $dataSource->default_params,
            'rate_limit' => $validated['rate_limit'] ?? $dataSource->rate_limit,
            'timeout' => $validated['timeout'] ?? $dataSource->timeout,
            'status' => $validated['status'] ?? $dataSource->status,
        ]);
        
        // 验证API配置
        $configErrors = $dataSource->validateApiConfig();
        if (!empty($configErrors)) {
            return response()->json([
                'success' => false,
                'message' => 'API配置验证失败',
                'errors' => $configErrors
            ], 422);
        }
        
        $dataSource->load('owner');
        
        return response()->json([
            'success' => true,
            'data' => $dataSource,
            'message' => '数据源更新成功'
        ]);
    }
    
    /**
     * 删除数据源
     */
    public function destroy(DataSource $dataSource): JsonResponse
    {
        // 检查是否有关联的监控任务
        if ($dataSource->monitoringTasks()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => '该数据源正在被监控任务使用，无法删除'
            ], 400);
        }
        
        // 删除关联的字段映射
        $dataSource->fieldMappings()->delete();
        
        // 删除数据源
        $dataSource->delete();
        
        return response()->json([
            'success' => true,
            'message' => '数据源删除成功'
        ]);
    }
    
    /**
     * 批量删除数据源
     */
    public function batchDestroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:data_sources,id',
        ]);
        
        $dataSources = DataSource::whereIn('id', $validated['ids'])->get();
        $deletedCount = 0;
        $errors = [];
        
        foreach ($dataSources as $dataSource) {
            if ($dataSource->monitoringTasks()->count() > 0) {
                $errors[] = "数据源 '{$dataSource->name}' 正在被监控任务使用，无法删除";
                continue;
            }
            
            $dataSource->fieldMappings()->delete();
            $dataSource->delete();
            $deletedCount++;
        }
        
        return response()->json([
            'success' => true,
            'data' => [
                'deleted_count' => $deletedCount,
                'errors' => $errors
            ],
            'message' => "成功删除 {$deletedCount} 个数据源"
        ]);
    }
    
    /**
     * 测试数据源连接
     */
    public function testConnection(DataSource $dataSource): JsonResponse
    {
        try {
            $headers = $dataSource->getRequestHeaders();
            $params = $dataSource->getRequestParams();
            
            // 发送测试请求
            $response = Http::withHeaders($headers)
                ->timeout($dataSource->timeout)
                ->get($dataSource->api_base_url, $params);
            
            $isSuccess = $response->successful();
            
            // 更新使用统计
            $dataSource->updateUsageStats($isSuccess);
            
            if ($isSuccess) {
                return response()->json([
                    'success' => true,
                    'data' => [
                        'status_code' => $response->status(),
                        'response_time' => $response->transferStats->getTransferTime(),
                        'response_size' => strlen($response->body()),
                    ],
                    'message' => '数据源连接测试成功'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'data' => [
                        'status_code' => $response->status(),
                        'error' => $response->body(),
                    ],
                    'message' => '数据源连接测试失败'
                ], 400);
            }
        } catch (\Exception $e) {
            // 更新使用统计
            $dataSource->updateUsageStats(false);
            
            return response()->json([
                'success' => false,
                'message' => '数据源连接测试失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 更新数据源状态
     */
    public function updateStatus(Request $request, DataSource $dataSource): JsonResponse
    {
        $validated = $request->validate([
            'status' => 'required|integer|in:0,1',
        ]);
        
        $dataSource->update(['status' => $validated['status']]);
        
        $statusText = $validated['status'] == 1 ? '启用' : '禁用';
        
        return response()->json([
            'success' => true,
            'data' => $dataSource,
            'message' => "数据源{$statusText}成功"
        ]);
    }
    
    /**
     * 获取数据源统计信息
     */
    public function statistics(DataSource $dataSource): JsonResponse
    {
        $stats = [
            'total_requests' => $dataSource->total_requests,
            'success_requests' => $dataSource->success_requests,
            'failed_requests' => $dataSource->total_requests - $dataSource->success_requests,
            'success_rate' => $dataSource->success_rate,
            'last_used_at' => $dataSource->last_used_at,
            'monitoring_tasks_count' => $dataSource->monitoringTasks()->count(),
            'field_mappings_count' => $dataSource->fieldMappings()->count(),
        ];
        
        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => '数据源统计信息获取成功'
        ]);
    }
    
    /**
     * 复制数据源
     */
    public function duplicate(DataSource $dataSource): JsonResponse
    {
        $newDataSource = $dataSource->replicate();
        $newDataSource->name = $dataSource->name . '_副本_' . time();
        $newDataSource->owner_id = Auth::id();
        $newDataSource->total_requests = 0;
        $newDataSource->success_requests = 0;
        $newDataSource->last_used_at = null;
        $newDataSource->save();
        
        // 复制字段映射
        foreach ($dataSource->fieldMappings as $fieldMapping) {
            $newFieldMapping = $fieldMapping->replicate();
            $newFieldMapping->data_source_id = $newDataSource->id;
            $newFieldMapping->save();
        }
        
        $newDataSource->load('owner', 'fieldMappings');
        
        return response()->json([
            'success' => true,
            'data' => $newDataSource,
            'message' => '数据源复制成功'
        ]);
    }

    /**
     * 获取所有数据源的简要列表
     */
    public function all(): JsonResponse
    {
        $dataSources = DataSource::where('status', 1)
            ->select('id', 'name', 'type')
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $dataSources,
            'message' => '所有数据源列表获取成功'
        ]);
    }
} 