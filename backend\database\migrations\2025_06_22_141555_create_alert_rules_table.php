<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('alert_rules', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('规则名称');
            $table->text('description')->nullable()->comment('规则描述');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade')->comment('创建者ID');
            $table->foreignId('monitoring_task_id')->constrained('monitoring_tasks')->onDelete('cascade')->comment('监控任务ID');
            
            // 规则配置
            $table->string('rule_type', 50)->comment('规则类型：price_deviation,stock_change,status_change等');
            $table->string('target_field', 100)->comment('目标字段');
            $table->string('operator', 20)->comment('操作符：>,<,>=,<=,=,!=,between等');
            $table->json('threshold_values')->comment('阈值配置');
            $table->json('condition_config')->nullable()->comment('条件配置');
            
            // 预警级别
            $table->string('severity', 20)->default('medium')->comment('严重级别：low,medium,high,critical');
            $table->string('priority', 20)->default('normal')->comment('优先级：low,normal,high,urgent');
            
            // 通知配置
            $table->json('notification_channels')->comment('通知渠道：email,sms,webhook等');
            $table->json('notification_config')->nullable()->comment('通知配置');
            $table->json('recipients')->comment('接收人配置');
            
            // 频率控制
            $table->integer('cooldown_minutes')->default(60)->comment('冷却时间(分钟)');
            $table->integer('max_alerts_per_hour')->default(10)->comment('每小时最大预警数');
            $table->integer('max_alerts_per_day')->default(100)->comment('每天最大预警数');
            
            // 状态统计
            $table->tinyInteger('status')->default(1)->comment('状态：1-启用，0-禁用');
            $table->integer('trigger_count')->default(0)->comment('触发次数');
            $table->timestamp('last_triggered_at')->nullable()->comment('最后触发时间');
            $table->timestamp('last_notification_at')->nullable()->comment('最后通知时间');
            
            $table->timestamps();
            
            // 索引
            $table->index(['user_id', 'status']);
            $table->index(['monitoring_task_id']);
            $table->index(['rule_type', 'status']);
            $table->index(['severity', 'priority']);
            $table->index(['last_triggered_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('alert_rules');
    }
};
