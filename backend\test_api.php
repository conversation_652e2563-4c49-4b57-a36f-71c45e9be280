<?php

// 测试数据源API的PHP脚本

$baseUrl = 'http://127.0.0.1:8000/api';

// 1. 登录获取token
echo "=== 测试登录 ===\n";
$loginData = [
    'username' => 'admin',
    'password' => 'admin123'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/auth/login');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($loginData));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: $response\n\n";

if ($httpCode !== 200) {
    echo "登录失败，停止测试\n";
    exit(1);
}

$loginResult = json_decode($response, true);
if (!isset($loginResult['data']['token'])) {
    echo "未获取到token，停止测试\n";
    exit(1);
}

$token = $loginResult['data']['token'];
echo "Token获取成功: " . substr($token, 0, 20) . "...\n\n";

// 2. 测试获取数据源列表
echo "=== 测试获取数据源列表 ===\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/data-sources');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Authorization: Bearer ' . $token
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: $response\n\n";

// 3. 测试创建数据源
echo "=== 测试创建数据源 ===\n";
$dataSourceData = [
    'name' => '测试淘宝数据源',
    'type' => 'taobao',
    'description' => '用于测试的淘宝API数据源',
    'api_base_url' => 'https://api.taobao.com/v1',
    'api_config' => [
        'app_key' => 'test_key',
        'app_secret' => 'test_secret'
    ],
    'headers' => [
        'User-Agent' => 'Test-Agent/1.0'
    ],
    'default_params' => [
        'format' => 'json',
        'version' => '2.0'
    ],
    'rate_limit' => 100,
    'timeout' => 30,
    'status' => 1
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/data-sources');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($dataSourceData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Authorization: Bearer ' . $token
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: $response\n\n";

// 4. 测试获取数据源类型
echo "=== 测试获取数据源类型 ===\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/data-sources/types');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Authorization: Bearer ' . $token
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: $response\n\n";

echo "=== API测试完成 ===\n"; 