<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AlertRule extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'user_id',
        'monitoring_task_id',
        'rule_type',
        'target_field',
        'operator',
        'threshold_values',
        'condition_config',
        'severity',
        'priority',
        'notification_channels',
        'notification_config',
        'recipients',
        'cooldown_minutes',
        'max_alerts_per_hour',
        'max_alerts_per_day',
        'status',
        'trigger_count',
        'last_triggered_at',
        'last_notification_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'threshold_values' => 'array',
        'condition_config' => 'array',
        'notification_channels' => 'array',
        'notification_config' => 'array',
        'recipients' => 'array',
        'cooldown_minutes' => 'integer',
        'max_alerts_per_hour' => 'integer',
        'max_alerts_per_day' => 'integer',
        'status' => 'integer',
        'trigger_count' => 'integer',
        'last_triggered_at' => 'datetime',
        'last_notification_at' => 'datetime',
    ];

    /**
     * 创建者
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取此规则所属的监控任务
     */
    public function monitoringTask(): BelongsTo
    {
        return $this->belongsTo(MonitoringTask::class, 'monitoring_task_id');
    }

    /**
     * 生成的预警
     */
    public function alerts(): HasMany
    {
        return $this->hasMany(Alert::class);
    }

    /**
     * 检查规则是否启用
     */
    public function isEnabled(): bool
    {
        return $this->status === 1;
    }

    /**
     * 检查规则是否禁用
     */
    public function isDisabled(): bool
    {
        return $this->status === 0;
    }

    /**
     * 检查是否在冷却期内
     */
    public function isInCooldown(): bool
    {
        if (!$this->last_triggered_at) {
            return false;
        }

        $cooldownUntil = $this->last_triggered_at->addMinutes($this->cooldown_minutes);
        return now()->lt($cooldownUntil);
    }

    /**
     * 检查今日预警次数是否已达上限
     */
    public function hasReachedDailyLimit(): bool
    {
        $todayAlerts = $this->alerts()
            ->whereDate('created_at', today())
            ->count();

        return $todayAlerts >= $this->max_alerts_per_day;
    }

    /**
     * 检查本小时预警次数是否已达上限
     */
    public function hasReachedHourlyLimit(): bool
    {
        $hourlyAlerts = $this->alerts()
            ->where('created_at', '>=', now()->startOfHour())
            ->count();

        return $hourlyAlerts >= $this->max_alerts_per_hour;
    }

    /**
     * 获取可用的规则类型
     */
    public static function getAvailableRuleTypes(): array
    {
        return [
            'price_deviation' => '价格偏差',
            'price_increase' => '价格上涨',
            'price_decrease' => '价格下跌',
            'stock_change' => '库存变化',
            'stock_out' => '缺货',
            'stock_low' => '库存不足',
            'status_change' => '状态变更',
            'availability_change' => '可用性变化',
            'rating_change' => '评分变化',
            'review_count_change' => '评论数量变化',
            'custom' => '自定义规则',
        ];
    }

    /**
     * 获取可用的操作符
     */
    public static function getAvailableOperators(): array
    {
        return [
            '>' => '大于',
            '<' => '小于',
            '>=' => '大于等于',
            '<=' => '小于等于',
            '=' => '等于',
            '!=' => '不等于',
            'between' => '在范围内',
            'not_between' => '不在范围内',
            'contains' => '包含',
            'not_contains' => '不包含',
            'starts_with' => '开始于',
            'ends_with' => '结束于',
            'regex' => '正则匹配',
            'change_rate' => '变化率',
            'percentage_change' => '百分比变化',
        ];
    }

    /**
     * 获取可用的严重级别
     */
    public static function getAvailableSeverities(): array
    {
        return [
            'low' => '低',
            'medium' => '中',
            'high' => '高',
            'critical' => '严重',
        ];
    }

    /**
     * 获取可用的优先级
     */
    public static function getAvailablePriorities(): array
    {
        return [
            'low' => '低',
            'normal' => '普通',
            'high' => '高',
            'urgent' => '紧急',
        ];
    }

    /**
     * 获取可用的通知渠道
     */
    public static function getAvailableNotificationChannels(): array
    {
        return [
            'email' => '邮件',
            'sms' => '短信',
            'webhook' => 'Webhook',
            'in_app' => '站内信',
            'slack' => 'Slack',
            'dingtalk' => '钉钉',
            'wechat' => '微信',
        ];
    }

    /**
     * 验证阈值配置
     */
    public function validateThresholdValues(): bool
    {
        if (!is_array($this->threshold_values)) {
            return false;
        }

        switch ($this->operator) {
            case 'between':
            case 'not_between':
                return isset($this->threshold_values['min']) && isset($this->threshold_values['max']);
            case 'change_rate':
            case 'percentage_change':
                return isset($this->threshold_values['rate']) || isset($this->threshold_values['percentage']);
            default:
                return isset($this->threshold_values['value']);
        }
    }

    /**
     * 格式化阈值显示
     */
    public function getFormattedThreshold(): string
    {
        switch ($this->operator) {
            case 'between':
                return "在 {$this->threshold_values['min']} 到 {$this->threshold_values['max']} 之间";
            case 'not_between':
                return "不在 {$this->threshold_values['min']} 到 {$this->threshold_values['max']} 之间";
            case 'change_rate':
                return "变化率 {$this->operator} {$this->threshold_values['rate']}%";
            case 'percentage_change':
                return "百分比变化 {$this->operator} {$this->threshold_values['percentage']}%";
            default:
                $operatorText = self::getAvailableOperators()[$this->operator] ?? $this->operator;
                return "{$operatorText} {$this->threshold_values['value']}";
        }
    }
} 