import apiClient from './apiClient';

const alertRuleApi = {
  getAlertRules(params) {
    return apiClient.get('/alert-rules', { params });
  },
  getAlertRule(id) {
    return apiClient.get(`/alert-rules/${id}`);
  },
  createAlertRule(data) {
    return apiClient.post('/alert-rules', data);
  },
  updateAlertRule(id, data) {
    return apiClient.put(`/alert-rules/${id}`, data);
  },
  deleteAlertRule(id) {
    return apiClient.delete(`/alert-rules/${id}`);
  },
  activateAlertRule(id) {
    return apiClient.patch(`/alert-rules/${id}/activate`);
  },
  deactivateAlertRule(id) {
    return apiClient.patch(`/alert-rules/${id}/deactivate`);
  },
  getAlertRuleOptions() {
    return apiClient.get('/alert-rules/options');
  }
};

const alertHistoryApi = {
  getAlerts(params) {
    return apiClient.get('/alerts', { params });
  },
  getAlert(id) {
    return apiClient.get(`/alerts/${id}`);
  },
  markAlertAsRead(id) {
    return apiClient.patch(`/alerts/${id}/read`);
  },
  batchDeleteAlerts(data) {
    return apiClient.delete('/alerts', { data });
  }
};

export default {
  ...alertRuleApi,
  ...alertHistoryApi
}; 