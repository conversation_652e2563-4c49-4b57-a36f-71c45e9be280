<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use App\Services\AuditLogService;
use App\Models\AuditLog;
use App\Models\User;
use App\Models\DataSource;

// 启动Laravel应用
$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== 审计日志系统测试 ===\n\n";

try {
    $auditLogService = app(AuditLogService::class);
    
    // 1. 测试系统操作日志
    echo "1. 测试系统操作日志记录...\n";
    $systemLog = $auditLogService->logSystemAction(
        AuditLog::ACTION_EXPORT,
        '系统导出用户数据',
        AuditLog::LEVEL_INFO,
        ['export_type' => 'csv', 'record_count' => 100]
    );
    
    if ($systemLog) {
        echo "   ✓ 系统操作日志记录成功 (ID: {$systemLog->id})\n";
    } else {
        echo "   ✗ 系统操作日志记录失败\n";
    }
    
    // 2. 测试认证操作日志
    echo "\n2. 测试认证操作日志记录...\n";
    $authLog = $auditLogService->logAuthAction(
        AuditLog::ACTION_LOGIN,
        1,
        [
            'user_name' => 'admin',
            'user_email' => '<EMAIL>',
            'guard' => 'web'
        ],
        AuditLog::LEVEL_INFO
    );
    
    if ($authLog) {
        echo "   ✓ 认证操作日志记录成功 (ID: {$authLog->id})\n";
    } else {
        echo "   ✗ 认证操作日志记录失败\n";
    }
    
    // 3. 测试批量日志记录
    echo "\n3. 测试批量日志记录...\n";
    $batchLogs = [
        [
            'user_id' => 1,
            'user_name' => 'admin',
            'action' => AuditLog::ACTION_BULK_UPDATE,
            'model_type' => null,
            'model_id' => null,
            'table_name' => null,
            'old_values' => null,
            'new_values' => null,
            'details' => ['batch_size' => 10],
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Agent',
            'url' => '/test',
            'method' => 'POST',
            'level' => AuditLog::LEVEL_INFO,
            'description' => '批量更新测试',
            'created_at' => now(),
            'updated_at' => now(),
        ],
        [
            'user_id' => 1,
            'user_name' => 'admin',
            'action' => AuditLog::ACTION_BULK_DELETE,
            'model_type' => null,
            'model_id' => null,
            'table_name' => null,
            'old_values' => null,
            'new_values' => null,
            'details' => ['batch_size' => 5],
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Agent',
            'url' => '/test',
            'method' => 'DELETE',
            'level' => AuditLog::LEVEL_WARNING,
            'description' => '批量删除测试',
            'created_at' => now(),
            'updated_at' => now(),
        ]
    ];
    
    $successCount = $auditLogService->logBatch($batchLogs);
    echo "   ✓ 批量日志记录成功，成功记录 {$successCount} 条\n";
    
    // 4. 测试日志查询
    echo "\n4. 测试日志查询功能...\n";
    $logs = $auditLogService->getAuditLogs([], 5);
    echo "   ✓ 查询到 {$logs->total()} 条日志记录\n";
    
    // 显示最新的几条日志
    echo "   最新的几条日志：\n";
    foreach ($logs->items() as $log) {
        echo "   - [{$log->level}] {$log->action}: {$log->description} ({$log->created_at})\n";
    }
    
    // 5. 测试统计功能
    echo "\n5. 测试统计功能...\n";
    $stats = $auditLogService->getAuditLogStatistics();
    echo "   ✓ 统计信息获取成功\n";
    echo "   - 总日志数: {$stats['total_logs']}\n";
    echo "   - 操作类型统计: " . count($stats['by_action']) . " 种操作\n";
    echo "   - 日志级别统计: " . count($stats['by_level']) . " 种级别\n";
    
    // 6. 测试用户操作统计
    echo "\n6. 测试用户操作统计...\n";
    $userStats = $auditLogService->getUserOperationStatistics();
    echo "   ✓ 用户操作统计获取成功，共 " . count($userStats) . " 个用户\n";
    
    // 7. 测试操作类型统计
    echo "\n7. 测试操作类型统计...\n";
    $actionStats = $auditLogService->getActionStatistics();
    echo "   ✓ 操作类型统计获取成功，共 " . count($actionStats) . " 种操作\n";
    
    // 8. 测试导出功能
    echo "\n8. 测试导出功能...\n";
    try {
        $exportPath = $auditLogService->exportAuditLogs([], 'csv');
        echo "   ✓ 日志导出成功，文件路径: {$exportPath}\n";
    } catch (Exception $e) {
        echo "   ✗ 日志导出失败: " . $e->getMessage() . "\n";
    }
    
    // 9. 测试清理功能
    echo "\n9. 测试清理功能...\n";
    $deletedCount = $auditLogService->cleanupOldLogs(365); // 清理一年前的日志
    echo "   ✓ 清理完成，删除了 {$deletedCount} 条过期日志\n";
    
    // 10. 测试模型审计（如果有User模型）
    echo "\n10. 测试模型审计功能...\n";
    try {
        // 创建一个测试用户来触发审计日志
        $user = User::create([
            'name' => 'Test User ' . time(),
            'email' => 'test' . time() . '@example.com',
            'password' => bcrypt('password'),
            'username' => 'testuser' . time(),
        ]);
        
        echo "   ✓ 创建用户成功，应该触发审计日志\n";
        
        // 更新用户
        $user->update(['name' => 'Updated Test User']);
        echo "   ✓ 更新用户成功，应该触发审计日志\n";
        
        // 删除用户
        $user->delete();
        echo "   ✓ 删除用户成功，应该触发审计日志\n";
        
    } catch (Exception $e) {
        echo "   ✗ 模型审计测试失败: " . $e->getMessage() . "\n";
    }
    
    // 11. 验证审计日志记录
    echo "\n11. 验证最新的审计日志记录...\n";
    $recentLogs = AuditLog::orderBy('created_at', 'desc')->limit(5)->get();
    echo "   最新的 5 条审计日志：\n";
    foreach ($recentLogs as $log) {
        echo "   - [{$log->level}] {$log->action}: {$log->description}\n";
        echo "     用户: {$log->user_name}, 时间: {$log->created_at}\n";
        if ($log->details) {
            echo "     详情: " . json_encode($log->details, JSON_UNESCAPED_UNICODE) . "\n";
        }
        echo "\n";
    }
    
    echo "=== 审计日志系统测试完成 ===\n";
    echo "所有功能测试通过！\n";
    
} catch (Exception $e) {
    echo "测试失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
} 