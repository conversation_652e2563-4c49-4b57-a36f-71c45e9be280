<?php

namespace App\Jobs;

use App\Models\DataSource;
use App\Models\MonitoringTask;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class ProcessDataSourceTask implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    /**
     * 任务最大尝试次数
     */
    public $tries = 3;

    /**
     * 任务超时时间（秒）
     */
    public $timeout = 300;

    /**
     * 数据源ID
     */
    public int $dataSourceId;

    /**
     * 监控任务ID
     */
    public ?int $monitoringTaskId;

    /**
     * 目标产品信息
     */
    public array $targetProducts;

    /**
     * 任务参数
     */
    public array $taskParams;

    /**
     * Create a new job instance.
     */
    public function __construct(
        int $dataSourceId,
        array $targetProducts = [],
        array $taskParams = [],
        ?int $monitoringTaskId = null
    ) {
        $this->dataSourceId = $dataSourceId;
        $this->targetProducts = $targetProducts;
        $this->taskParams = $taskParams;
        $this->monitoringTaskId = $monitoringTaskId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('开始处理数据源任务', [
            'data_source_id' => $this->dataSourceId,
            'monitoring_task_id' => $this->monitoringTaskId,
            'target_products' => $this->targetProducts,
        ]);

        try {
            // 获取数据源
            $dataSource = DataSource::find($this->dataSourceId);
            if (!$dataSource) {
                throw new \Exception("数据源不存在: {$this->dataSourceId}");
            }

            if (!$dataSource->isAvailable()) {
                throw new \Exception("数据源不可用: {$dataSource->name}");
            }

            // 获取监控任务（如果存在）
            $monitoringTask = null;
            if ($this->monitoringTaskId) {
                $monitoringTask = MonitoringTask::find($this->monitoringTaskId);
            }

            // 处理每个目标产品
            $results = [];
            foreach ($this->targetProducts as $product) {
                $result = $this->processProduct($dataSource, $product);
                $results[] = $result;
            }

            // 更新数据源使用统计
            $dataSource->updateUsageStats(true);

            // 更新监控任务状态（如果存在）
            if ($monitoringTask) {
                $monitoringTask->increment('run_count');
                $monitoringTask->increment('success_count');
                $monitoringTask->update([
                    'last_run_at' => now(),
                    'next_run_at' => $this->calculateNextRunTime($monitoringTask),
                ]);
            }

            Log::info('数据源任务处理完成', [
                'data_source_id' => $this->dataSourceId,
                'results_count' => count($results),
            ]);

        } catch (\Exception $e) {
            // 更新数据源使用统计（失败）
            $dataSource = DataSource::find($this->dataSourceId);
            if ($dataSource) {
                $dataSource->updateUsageStats(false);
            }

            // 更新监控任务状态（如果存在）
            if ($this->monitoringTaskId) {
                $monitoringTask = MonitoringTask::find($this->monitoringTaskId);
                if ($monitoringTask) {
                    $monitoringTask->increment('run_count');
                    $monitoringTask->increment('failed_count');
                    $monitoringTask->update([
                        'last_run_at' => now(),
                        'last_error' => $e->getMessage(),
                    ]);
                }
            }

            Log::error('数据源任务处理失败', [
                'data_source_id' => $this->dataSourceId,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * 处理单个产品
     */
    private function processProduct(DataSource $dataSource, array $product): array
    {
        $startTime = microtime(true);

        try {
            // 构建API URL
            $endpoint = $this->buildEndpoint($product);
            $url = $dataSource->getFullApiUrl($endpoint);

            // 准备请求参数
            $params = $dataSource->getRequestParams($this->taskParams);
            $headers = $dataSource->getRequestHeaders();

            // 发送HTTP请求
            $response = Http::timeout($dataSource->timeout)
                ->withHeaders($headers)
                ->get($url, $params);

            if ($response->successful()) {
                $data = $response->json();
                
                // 处理响应数据
                $processedData = $this->processResponseData($data, $product);
                
                $duration = microtime(true) - $startTime;
                
                Log::info('产品数据获取成功', [
                    'product_id' => $product['id'] ?? 'unknown',
                    'data_source' => $dataSource->name,
                    'duration' => round($duration, 3),
                ]);

                return [
                    'success' => true,
                    'product' => $product,
                    'data' => $processedData,
                    'duration' => $duration,
                ];
            } else {
                throw new \Exception("API请求失败: HTTP {$response->status()}");
            }

        } catch (\Exception $e) {
            $duration = microtime(true) - $startTime;
            
            Log::error('产品数据获取失败', [
                'product_id' => $product['id'] ?? 'unknown',
                'data_source' => $dataSource->name,
                'error' => $e->getMessage(),
                'duration' => round($duration, 3),
            ]);

            return [
                'success' => false,
                'product' => $product,
                'error' => $e->getMessage(),
                'duration' => $duration,
            ];
        }
    }

    /**
     * 构建API端点
     */
    private function buildEndpoint(array $product): string
    {
        // 根据产品信息构建API端点
        $productId = $product['id'] ?? '';
        $productType = $product['type'] ?? 'product';
        
        return "/api/v1/{$productType}/{$productId}";
    }

    /**
     * 处理响应数据
     */
    private function processResponseData(array $data, array $product): array
    {
        // 这里可以根据数据源类型和产品类型进行不同的数据处理
        // 例如：数据清洗、格式转换、字段映射等
        
        return [
            'raw_data' => $data,
            'processed_at' => now()->toISOString(),
            'product_info' => $product,
        ];
    }

    /**
     * 计算下次运行时间
     */
    private function calculateNextRunTime(MonitoringTask $task): ?\Carbon\Carbon
    {
        if (!$task->cron_expression) {
            return null;
        }

        try {
            $cron = new \Cron\CronExpression($task->cron_expression);
            return \Carbon\Carbon::instance($cron->getNextRunDate());
        } catch (\Exception $e) {
            Log::warning('无效的cron表达式', [
                'task_id' => $task->id,
                'cron_expression' => $task->cron_expression,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * 任务失败时的处理
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('数据源任务最终失败', [
            'data_source_id' => $this->dataSourceId,
            'monitoring_task_id' => $this->monitoringTaskId,
            'exception' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }
}
