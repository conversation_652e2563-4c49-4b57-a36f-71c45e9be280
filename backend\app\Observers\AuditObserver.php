<?php

namespace App\Observers;

use App\Models\AuditLog;
use App\Services\AuditLogService;
use Illuminate\Database\Eloquent\Model;

class AuditObserver
{
    protected AuditLogService $auditLogService;

    public function __construct(AuditLogService $auditLogService)
    {
        $this->auditLogService = $auditLogService;
    }

    /**
     * 处理模型创建事件
     *
     * @param Model $model
     * @return void
     */
    public function created(Model $model): void
    {
        $this->auditLogService->logModelAction(
            AuditLog::ACTION_CREATE,
            $model,
            [],
            $this->getModelAttributes($model),
            AuditLog::LEVEL_INFO
        );
    }

    /**
     * 处理模型更新事件
     *
     * @param Model $model
     * @return void
     */
    public function updated(Model $model): void
    {
        // 只记录实际发生变化的字段
        $changes = $model->getChanges();
        $original = $model->getOriginal();
        
        if (!empty($changes)) {
            $oldValues = [];
            $newValues = [];
            
            foreach ($changes as $key => $newValue) {
                $oldValues[$key] = $original[$key] ?? null;
                $newValues[$key] = $newValue;
            }

            $this->auditLogService->logModelAction(
                AuditLog::ACTION_UPDATE,
                $model,
                $oldValues,
                $newValues,
                AuditLog::LEVEL_INFO
            );
        }
    }

    /**
     * 处理模型删除事件
     *
     * @param Model $model
     * @return void
     */
    public function deleted(Model $model): void
    {
        $this->auditLogService->logModelAction(
            AuditLog::ACTION_DELETE,
            $model,
            $this->getModelAttributes($model),
            [],
            AuditLog::LEVEL_WARNING
        );
    }

    /**
     * 获取模型属性（排除敏感信息）
     *
     * @param Model $model
     * @return array
     */
    private function getModelAttributes(Model $model): array
    {
        $attributes = $model->getAttributes();
        
        // 排除敏感字段
        $sensitiveFields = ['password', 'remember_token', 'api_token'];
        
        foreach ($sensitiveFields as $field) {
            if (isset($attributes[$field])) {
                unset($attributes[$field]);
            }
        }

        return $attributes;
    }
} 