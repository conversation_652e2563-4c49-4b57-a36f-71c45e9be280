<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

// 创建Laravel应用实例
$app = Application::configure(basePath: __DIR__)
    ->withRouting(
        web: __DIR__.'/routes/web.php',
        api: __DIR__.'/routes/api.php',
        commands: __DIR__.'/routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();

$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Models\DataSource;
use App\Models\MonitoringTask;
use App\Models\AlertRule;
use App\Models\ProductData;
use App\Services\AlertService;
use Illuminate\Support\Facades\DB;

echo "开始测试预警系统...\n\n";

try {
    // 1. 创建测试用户
    echo "1. 创建测试用户...\n";
    $user = User::firstOrCreate(
        ['email' => '<EMAIL>'],
        [
            'name' => '测试用户',
            'username' => 'testuser',
            'password' => bcrypt('password'),
        ]
    );
    echo "用户ID: {$user->id}\n\n";

    // 2. 创建测试数据源
    echo "2. 创建测试数据源...\n";
    $dataSource = DataSource::firstOrCreate(
        ['name' => '测试电商平台'],
        [
            'type' => 'api',
            'api_base_url' => 'https://api.test.com',
            'api_endpoint_template' => '/products/{item_id}',
            'api_method' => 'GET',
            'timeout' => 30,
            'status' => 'active',
        ]
    );
    echo "数据源ID: {$dataSource->id}\n\n";

    // 3. 创建监控任务
    echo "3. 创建监控任务...\n";
    $task = MonitoringTask::firstOrCreate(
        [
            'name' => '价格监控测试任务',
            'user_id' => $user->id,
            'data_source_id' => $dataSource->id,
        ],
        [
            'description' => '测试价格监控功能',
            'target_products' => ['TEST001', 'TEST002'],
            'frequency_type' => 'minutes',
            'frequency_value' => 30,
            'status' => 'active',
        ]
    );
    echo "监控任务ID: {$task->id}\n\n";

    // 4. 创建预警规则
    echo "4. 创建预警规则...\n";
    $alertRule = AlertRule::firstOrCreate(
        [
            'name' => '价格低于100元预警',
            'monitoring_task_id' => $task->id,
        ],
        [
            'description' => '当产品价格低于100元时触发预警',
            'user_id' => $user->id,
            'rule_type' => 'price_decrease',
            'target_field' => 'price',
            'operator' => '<',
            'threshold_values' => [100],
            'severity' => 'medium',
            'priority' => 2,
            'cooldown_minutes' => 30,
            'max_alerts_per_hour' => 5,
            'max_alerts_per_day' => 20,
            'status' => 1, // 启用
        ]
    );
    echo "预警规则ID: {$alertRule->id}\n\n";

    // 5. 创建测试产品数据（价格为99，应该触发预警）
    echo "5. 创建测试产品数据（价格99元，应该触发预警）...\n";
    $productData = ProductData::create([
        'data_source_id' => $dataSource->id,
        'item_id' => 'TEST001',
        'raw_data' => [
            'id' => 'TEST001',
            'name' => '测试商品',
            'price' => 99.00,
            'stock' => 10,
            'status' => '在售',
        ],
        'standardized_data' => [
            'title' => '测试商品',
            'price' => 99.00,
            'quantity' => 10,
            'status' => '在售',
        ],
        'last_collected_at' => now(),
    ]);
    echo "产品数据ID: {$productData->id}\n\n";

    // 6. 测试预警服务
    echo "6. 执行预警检查...\n";
    $alertService = new AlertService();
    $result = $alertService->processAlerts($productData);
    
    echo "预警检查结果:\n";
    echo "- 成功: " . ($result['success'] ? '是' : '否') . "\n";
    echo "- 触发预警数量: " . ($result['alerts_count'] ?? 0) . "\n";
    
    if (isset($result['triggered_alerts']) && count($result['triggered_alerts']) > 0) {
        echo "- 触发的预警:\n";
        foreach ($result['triggered_alerts'] as $alert) {
            echo "  * 预警ID: {$alert->id}\n";
            echo "  * 标题: {$alert->title}\n";
            echo "  * 消息: {$alert->message}\n";
            echo "  * 级别: {$alert->alert_level}\n";
        }
    }
    echo "\n";

    // 7. 创建另一个产品数据（价格为150，不应该触发预警）
    echo "7. 创建另一个产品数据（价格150元，不应该触发预警）...\n";
    $productData2 = ProductData::create([
        'data_source_id' => $dataSource->id,
        'item_id' => 'TEST002',
        'raw_data' => [
            'id' => 'TEST002',
            'name' => '测试商品2',
            'price' => 150.00,
            'stock' => 5,
            'status' => '在售',
        ],
        'standardized_data' => [
            'title' => '测试商品2',
            'price' => 150.00,
            'quantity' => 5,
            'status' => '在售',
        ],
        'last_collected_at' => now(),
    ]);
    echo "产品数据ID: {$productData2->id}\n\n";

    // 8. 测试第二个产品的预警检查
    echo "8. 执行第二个产品的预警检查...\n";
    $result2 = $alertService->processAlerts($productData2);
    
    echo "预警检查结果:\n";
    echo "- 成功: " . ($result2['success'] ? '是' : '否') . "\n";
    echo "- 触发预警数量: " . ($result2['alerts_count'] ?? 0) . "\n";
    echo "\n";

    // 9. 查看所有生成的预警
    echo "9. 查看所有生成的预警记录...\n";
    $alerts = DB::table('alerts')
        ->join('alert_rules', 'alerts.alert_rule_id', '=', 'alert_rules.id')
        ->join('monitoring_tasks', 'alerts.monitoring_task_id', '=', 'monitoring_tasks.id')
        ->select([
            'alerts.id',
            'alerts.title',
            'alerts.message',
            'alerts.alert_level',
            'alerts.status',
            'alerts.created_at',
            'alert_rules.name as rule_name',
            'monitoring_tasks.name as task_name'
        ])
        ->orderBy('alerts.created_at', 'desc')
        ->get();

    if ($alerts->count() > 0) {
        echo "共找到 {$alerts->count()} 条预警记录:\n";
        foreach ($alerts as $alert) {
            echo "- 预警ID: {$alert->id}\n";
            echo "  规则: {$alert->rule_name}\n";
            echo "  任务: {$alert->task_name}\n";
            echo "  标题: {$alert->title}\n";
            echo "  消息: {$alert->message}\n";
            echo "  级别: {$alert->alert_level}\n";
            echo "  状态: " . ($alert->status == 0 ? '未处理' : '已处理') . "\n";
            echo "  创建时间: {$alert->created_at}\n";
            echo "\n";
        }
    } else {
        echo "没有找到预警记录。\n\n";
    }

    echo "预警系统测试完成！\n";

} catch (Exception $e) {
    echo "测试过程中发生错误: " . $e->getMessage() . "\n";
    echo "错误追踪: " . $e->getTraceAsString() . "\n";
} 