<?php

namespace App\Services;

use App\Models\ProductData;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class AnalyticsService
{
    /**
     * 缓存过期时间（小时）
     */
    const CACHE_TTL = 1;

    /**
     * 计算促销价偏离率（SKU级）
     * 公式：(Price - subPrice) / Price × 100%
     * 
     * @param array $skuData SKU数据
     * @return float
     */
    public function calculatePromotionPriceDeviationRate(array $skuData): float
    {
        $price = $skuData['Price'] ?? 0;
        $subPrice = $skuData['subPrice'] ?? $price;

        if ($price == 0) {
            return 0;
        }

        return (($price - $subPrice) / $price) * 100;
    }

    /**
     * 计算渠道价格偏离率（SKU级）
     * 公式：（官方指导价 - subPrice）/ 官方指导价 × 100%
     * 
     * @param array $skuData SKU数据
     * @param float $officialPrice 官方指导价
     * @return float
     */
    public function calculateChannelPriceDeviationRate(array $skuData, float $officialPrice): float
    {
        if ($officialPrice == 0) {
            return 0;
        }

        $subPrice = $skuData['subPrice'] ?? ($skuData['Price'] ?? 0);
        return (($officialPrice - $subPrice) / $officialPrice) * 100;
    }

    /**
     * 分析促销策略倾向
     * 解析Promotion字段中的促销类型，统计使用频率和占比
     * 
     * @param Collection $productDataCollection 产品数据集合
     * @return array
     */
    public function analyzePromotionStrategyTrend(Collection $productDataCollection): array
    {
        $cacheKey = 'promotion_strategy_trend_' . md5($productDataCollection->pluck('id')->implode(','));
        
        return Cache::remember($cacheKey, self::CACHE_TTL * 3600, function () use ($productDataCollection) {
            $promotionTypes = [];
            $promotionStrategies = [];
            $totalCount = 0;

            foreach ($productDataCollection as $productData) {
                $standardizedData = $productData->standardized_data;
                
                if (isset($standardizedData['Promotion']) && is_array($standardizedData['Promotion'])) {
                    foreach ($standardizedData['Promotion'] as $promotion) {
                        $totalCount++;
                        
                        // 统计促销类型（content）
                        $content = $promotion['content'] ?? '未知';
                        $promotionTypes[$content] = ($promotionTypes[$content] ?? 0) + 1;
                        
                        // 统计促销策略（sub_content）
                        $subContent = $promotion['sub_content'] ?? '未知';
                        $promotionStrategies[$subContent] = ($promotionStrategies[$subContent] ?? 0) + 1;
                    }
                }
            }

            // 计算占比
            $typePercentages = [];
            foreach ($promotionTypes as $type => $count) {
                $typePercentages[$type] = [
                    'count' => $count,
                    'percentage' => $totalCount > 0 ? round(($count / $totalCount) * 100, 2) : 0
                ];
            }

            $strategyPercentages = [];
            foreach ($promotionStrategies as $strategy => $count) {
                $strategyPercentages[$strategy] = [
                    'count' => $count,
                    'percentage' => $totalCount > 0 ? round(($count / $totalCount) * 100, 2) : 0
                ];
            }

            // 按使用频率排序
            arsort($typePercentages);
            arsort($strategyPercentages);

            return [
                'promotion_types' => $typePercentages,
                'promotion_strategies' => $strategyPercentages,
                'total_promotions' => $totalCount
            ];
        });
    }

    /**
     * 计算单品价格综合折扣率（SKU级）
     * 公式：(1 − (ΣsubPricei / ΣPricei)) * 100%
     * 
     * @param array $productData 单品数据（包含所有SKU）
     * @return float
     */
    public function calculateProductComprehensiveDiscountRate(array $productData): float
    {
        $totalSubPrice = 0;
        $totalPrice = 0;

        if (isset($productData['skus']) && is_array($productData['skus'])) {
            foreach ($productData['skus'] as $sku) {
                $totalSubPrice += $sku['subPrice'] ?? ($sku['Price'] ?? 0);
                $totalPrice += $sku['Price'] ?? 0;
            }
        } else {
            // 如果没有SKU数组，使用单个产品的价格
            $totalSubPrice = $productData['subPrice'] ?? ($productData['Price'] ?? 0);
            $totalPrice = $productData['Price'] ?? 0;
        }

        if ($totalPrice == 0) {
            return 0;
        }

        return (1 - ($totalSubPrice / $totalPrice)) * 100;
    }

    /**
     * 计算总体促销强度指数（SKU级）
     * 公式：(促销SKU数 / 总SKU数) × (1 − 平均subPrice / 平均Price)
     * 
     * @param Collection $productDataCollection 产品数据集合
     * @return float
     */
    public function calculateOverallPromotionIntensityIndex(Collection $productDataCollection): float
    {
        $cacheKey = 'promotion_intensity_index_' . md5($productDataCollection->pluck('id')->implode(','));
        
        return Cache::remember($cacheKey, self::CACHE_TTL * 3600, function () use ($productDataCollection) {
            $totalSkuCount = 0;
            $promotionSkuCount = 0;
            $totalSubPrice = 0;
            $totalPrice = 0;

            foreach ($productDataCollection as $productData) {
                $standardizedData = $productData->standardized_data;
                
                if (isset($standardizedData['skus']) && is_array($standardizedData['skus'])) {
                    foreach ($standardizedData['skus'] as $sku) {
                        $totalSkuCount++;
                        $price = $sku['Price'] ?? 0;
                        $subPrice = $sku['subPrice'] ?? $price;
                        
                        $totalPrice += $price;
                        $totalSubPrice += $subPrice;
                        
                        // 如果subPrice < Price，认为是促销SKU
                        if ($subPrice < $price) {
                            $promotionSkuCount++;
                        }
                    }
                } else {
                    // 处理单SKU情况
                    $totalSkuCount++;
                    $price = $standardizedData['Price'] ?? 0;
                    $subPrice = $standardizedData['subPrice'] ?? $price;
                    
                    $totalPrice += $price;
                    $totalSubPrice += $subPrice;
                    
                    if ($subPrice < $price) {
                        $promotionSkuCount++;
                    }
                }
            }

            if ($totalSkuCount == 0 || $totalPrice == 0) {
                return 0;
            }

            $promotionRatio = $promotionSkuCount / $totalSkuCount;
            $avgSubPrice = $totalSubPrice / $totalSkuCount;
            $avgPrice = $totalPrice / $totalSkuCount;
            
            $discountRate = 1 - ($avgSubPrice / $avgPrice);

            return $promotionRatio * $discountRate * 100;
        });
    }

    /**
     * 计算单品价格综合偏差率（SKU级）
     * 公式：（我方指导价 - subPrice）/ 我方指导价 × 100%
     * 
     * @param array $productData 产品数据
     * @param float $ourGuidePrice 我方指导价
     * @return float
     */
    public function calculateProductPriceDeviationRate(array $productData, float $ourGuidePrice): float
    {
        if ($ourGuidePrice == 0) {
            return 0;
        }

        $subPrice = $productData['subPrice'] ?? ($productData['Price'] ?? 0);
        return (($ourGuidePrice - $subPrice) / $ourGuidePrice) * 100;
    }

    /**
     * 计算单品最低价偏差率（SKU级）
     * 公式：(我方最低指导价 - 竞品最低价subPrice) / 我方最低指导价 × 100%
     * 
     * @param array $productData 产品数据
     * @param float $ourMinPrice 我方最低指导价
     * @return float
     */
    public function calculateProductMinPriceDeviationRate(array $productData, float $ourMinPrice): float
    {
        if ($ourMinPrice == 0) {
            return 0;
        }

        $competitorMinPrice = $productData['最低到手价'] ?? ($productData['subPrice'] ?? ($productData['Price'] ?? 0));
        return (($ourMinPrice - $competitorMinPrice) / $ourMinPrice) * 100;
    }

    /**
     * 计算单品最高价偏差率（SKU级）
     * 公式：(我方最高指导价 - 竞品最高价SubPrice) / 我方最高指导价 × 100%
     * 
     * @param array $productData 产品数据
     * @param float $ourMaxPrice 我方最高指导价
     * @return float
     */
    public function calculateProductMaxPriceDeviationRate(array $productData, float $ourMaxPrice): float
    {
        if ($ourMaxPrice == 0) {
            return 0;
        }

        $competitorMaxPrice = $productData['最高到手价'] ?? ($productData['subPrice'] ?? ($productData['Price'] ?? 0));
        return (($ourMaxPrice - $competitorMaxPrice) / $ourMaxPrice) * 100;
    }

    /**
     * 计算竞品价格趋势斜率
     * 使用线性回归计算价格趋势
     * 
     * @param string $itemId 商品ID
     * @param int $days 分析天数
     * @return array
     */
    public function calculatePriceTrendSlope(string $itemId, int $days = 30): array
    {
        $cacheKey = "price_trend_slope_{$itemId}_{$days}";
        
        return Cache::remember($cacheKey, self::CACHE_TTL * 3600, function () use ($itemId, $days) {
            $startDate = Carbon::now()->subDays($days);
            
            $productDataCollection = ProductData::where('item_id', $itemId)
                ->where('last_collected_at', '>=', $startDate)
                ->orderBy('last_collected_at')
                ->get();

            if ($productDataCollection->count() < 2) {
                return [
                    'slope' => 0,
                    'trend' => 'stable',
                    'data_points' => $productDataCollection->count(),
                    'r_squared' => 0
                ];
            }

            $dataPoints = [];
            foreach ($productDataCollection as $index => $productData) {
                $standardizedData = $productData->standardized_data;
                $price = $standardizedData['subPrice'] ?? ($standardizedData['Price'] ?? 0);
                $dataPoints[] = [
                    'x' => $index + 1, // 时间点
                    'y' => $price,     // 价格
                    'date' => $productData->last_collected_at->format('Y-m-d')
                ];
            }

            // 计算线性回归
            $regression = $this->calculateLinearRegression($dataPoints);
            
            // 判断趋势
            $trend = 'stable';
            if ($regression['slope'] > 0.1) {
                $trend = 'rising';
            } elseif ($regression['slope'] < -0.1) {
                $trend = 'falling';
            }

            return [
                'slope' => round($regression['slope'], 4),
                'intercept' => round($regression['intercept'], 2),
                'trend' => $trend,
                'data_points' => count($dataPoints),
                'r_squared' => round($regression['r_squared'], 4),
                'price_data' => $dataPoints
            ];
        });
    }

    /**
     * 计算线性回归
     * 
     * @param array $dataPoints 数据点 [['x' => x1, 'y' => y1], ...]
     * @return array
     */
    private function calculateLinearRegression(array $dataPoints): array
    {
        $n = count($dataPoints);
        if ($n < 2) {
            return ['slope' => 0, 'intercept' => 0, 'r_squared' => 0];
        }

        $sumX = 0;
        $sumY = 0;
        $sumXY = 0;
        $sumXX = 0;
        $sumYY = 0;

        foreach ($dataPoints as $point) {
            $x = $point['x'];
            $y = $point['y'];
            
            $sumX += $x;
            $sumY += $y;
            $sumXY += $x * $y;
            $sumXX += $x * $x;
            $sumYY += $y * $y;
        }

        $meanX = $sumX / $n;
        $meanY = $sumY / $n;

        // 计算斜率和截距
        $denominator = $sumXX - $n * $meanX * $meanX;
        if ($denominator == 0) {
            return ['slope' => 0, 'intercept' => $meanY, 'r_squared' => 0];
        }

        $slope = ($sumXY - $n * $meanX * $meanY) / $denominator;
        $intercept = $meanY - $slope * $meanX;

        // 计算R²
        $ssRes = 0; // 残差平方和
        $ssTot = 0; // 总平方和
        
        foreach ($dataPoints as $point) {
            $x = $point['x'];
            $y = $point['y'];
            $predicted = $slope * $x + $intercept;
            
            $ssRes += pow($y - $predicted, 2);
            $ssTot += pow($y - $meanY, 2);
        }

        $rSquared = $ssTot != 0 ? 1 - ($ssRes / $ssTot) : 0;

        return [
            'slope' => $slope,
            'intercept' => $intercept,
            'r_squared' => $rSquared
        ];
    }

    /**
     * 获取价格趋势图表数据
     * 
     * @param array $itemIds 商品ID数组
     * @param int $days 分析天数
     * @return array
     */
    public function getPriceTrendChartData(array $itemIds, int $days = 30): array
    {
        $cacheKey = 'price_trend_chart_data_' . md5(implode(',', $itemIds) . '_' . $days);

        return Cache::remember($cacheKey, self::CACHE_TTL * 3600, function () use ($itemIds, $days) {
            $startDate = Carbon::now()->subDays($days)->startOfDay();

            $allProductData = ProductData::whereIn('item_id', $itemIds)
                ->where('created_at', '>=', $startDate)
                ->select('item_id', 'price', 'created_at')
                ->orderBy('created_at', 'asc')
                ->get();

            $groupedData = $allProductData->groupBy('item_id');

            $chartData = [
                'labels' => [],
                'datasets' => []
            ];

            $allLabels = $allProductData->pluck('created_at')->map(function ($date) {
                return $date->format('Y-m-d');
            })->unique()->sort()->values();

            $chartData['labels'] = $allLabels;
            
            foreach ($groupedData as $itemId => $data) {
                $dataset = [
                    'label' => 'Item ' . $itemId,
                    'data' => [],
                    'borderColor' => 'rgba(' . rand(0, 255) . ',' . rand(0, 255) . ',' . rand(0, 255) . ', 1)',
                    'fill' => false,
                ];

                $priceMap = $data->pluck('price', 'created_at');
                
                $finalData = [];
                $lastPrice = null;

                // Create a map of prices by date string for efficient lookup
                $datePriceMap = $data->mapWithKeys(function ($item) {
                     return [$item->created_at->format('Y-m-d') => $item->price];
                });

                foreach ($allLabels as $label) {
                    if (isset($datePriceMap[$label])) {
                        $currentPrice = $datePriceMap[$label];
                        $finalData[] = $currentPrice;
                        $lastPrice = $currentPrice;
                    } else {
                        $finalData[] = $lastPrice; // Use last known price for missing dates
                    }
                }
                
                $dataset['data'] = $finalData;
                $chartData['datasets'][] = $dataset;
            }

            return $chartData;
        });
    }

    /**
     * 获取类目价格带分布数据
     * 
     * @param array $priceRanges 价格区间 [[0, 50], [50, 100], ...]
     * @return array
     */
    public function getCategoryPriceDistribution(array $priceRanges = null): array
    {
        if ($priceRanges === null) {
            $priceRanges = [
                [0, 50],
                [50, 100],
                [100, 200],
                [200, 500],
                [500, 1000],
                [1000, 2000],
                [2000, 5000],
                [5000, 999999]
            ];
        }

        $cacheKey = 'category_price_distribution_' . md5(json_encode($priceRanges));
        
        return Cache::remember($cacheKey, self::CACHE_TTL * 3600, function () use ($priceRanges) {
            $productDataCollection = ProductData::whereNotNull('standardized_data')
                ->get();

            $distribution = [];
            
            foreach ($productDataCollection as $productData) {
                $standardizedData = $productData->standardized_data;
                $categoryPath = $standardizedData['category_Path'] ?? '未分类';
                $price = $standardizedData['subPrice'] ?? ($standardizedData['Price'] ?? 0);

                // 确定价格区间
                $priceRange = null;
                foreach ($priceRanges as $range) {
                    if ($price >= $range[0] && $price < $range[1]) {
                        $priceRange = "{$range[0]}-{$range[1]}";
                        break;
                    }
                }

                if ($priceRange) {
                    if (!isset($distribution[$categoryPath])) {
                        $distribution[$categoryPath] = [];
                    }
                    if (!isset($distribution[$categoryPath][$priceRange])) {
                        $distribution[$categoryPath][$priceRange] = 0;
                    }
                    $distribution[$categoryPath][$priceRange]++;
                }
            }

            return $distribution;
        });
    }

    /**
     * 生成综合分析报告
     * 
     * @param array $itemIds 商品ID数组
     * @param array $options 分析选项
     * @return array
     */
    public function generateComprehensiveAnalysisReport(array $itemIds, array $options = []): array
    {
        $cacheKey = 'comprehensive_analysis_' . md5(implode(',', $itemIds) . json_encode($options));
        
        return Cache::remember($cacheKey, self::CACHE_TTL * 3600, function () use ($itemIds, $options) {
            $productDataCollection = ProductData::whereIn('item_id', $itemIds)
                ->orderBy('last_collected_at', 'desc')
                ->get()
                ->groupBy('item_id')
                ->map(function ($group) {
                    return $group->first(); // 取每个商品的最新数据
                });

            $report = [
                'summary' => [
                    'total_products' => count($itemIds),
                    'analyzed_products' => $productDataCollection->count(),
                    'analysis_date' => Carbon::now()->toDateTimeString()
                ],
                'promotion_analysis' => $this->analyzePromotionStrategyTrend($productDataCollection),
                'price_analysis' => [],
                'trend_analysis' => []
            ];

            // 价格分析
            foreach ($productDataCollection as $itemId => $productData) {
                $standardizedData = $productData->standardized_data;
                
                $report['price_analysis'][$itemId] = [
                    'product_name' => $standardizedData['Title'] ?? "商品{$itemId}",
                    'current_price' => $standardizedData['Price'] ?? 0,
                    'current_sub_price' => $standardizedData['subPrice'] ?? 0,
                    'promotion_deviation_rate' => $this->calculatePromotionPriceDeviationRate($standardizedData),
                    'comprehensive_discount_rate' => $this->calculateProductComprehensiveDiscountRate($standardizedData)
                ];

                // 如果提供了我方指导价，计算偏差率
                if (isset($options['our_guide_prices'][$itemId])) {
                    $ourGuidePrice = $options['our_guide_prices'][$itemId];
                    $report['price_analysis'][$itemId]['price_deviation_rate'] = 
                        $this->calculateProductPriceDeviationRate($standardizedData, $ourGuidePrice);
                }
            }

            // 趋势分析
            if ($options['include_trend_analysis'] ?? false) {
                $days = $options['trend_days'] ?? 30;
                foreach ($itemIds as $itemId) {
                    $report['trend_analysis'][$itemId] = $this->calculatePriceTrendSlope($itemId, $days);
                }
            }

            // 整体促销强度指数
            $report['overall_promotion_intensity'] = $this->calculateOverallPromotionIntensityIndex($productDataCollection);

            return $report;
        });
    }

    /**
     * 清除分析缓存
     * 
     * @param string|null $pattern 缓存键模式
     * @return bool
     */
    public function clearAnalysisCache(string $pattern = null): bool
    {
        try {
            if ($pattern) {
                // 清除特定模式的缓存
                Cache::forget($pattern);
            } else {
                // 清除所有分析相关的缓存
                $cacheKeys = [
                    'promotion_strategy_trend_*',
                    'promotion_intensity_index_*',
                    'price_trend_slope_*',
                    'price_trend_chart_*',
                    'category_price_distribution_*',
                    'comprehensive_analysis_*'
                ];
                
                foreach ($cacheKeys as $key) {
                    Cache::forget($key);
                }
            }
            
            Log::info('Analytics cache cleared', ['pattern' => $pattern]);
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to clear analytics cache', [
                'pattern' => $pattern,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取仪表盘统计数据
     *
     * @return array
     */
    public function getDashboardStatistics(): array
    {
        $cacheKey = 'dashboard_statistics';
        
        return Cache::remember($cacheKey, self::CACHE_TTL * 3600, function () {
            $userId = auth()->id();

            $totalTasks = \App\Models\MonitoringTask::where('user_id', $userId)->count();
            $activeTasks = \App\Models\MonitoringTask::where('user_id', $userId)->where('status', 'running')->count();
            
            $totalDataSources = \App\Models\DataSource::count(); // 通常是全局的
            
            $productsToday = ProductData::whereDate('created_at', Carbon::today())->count();
            
            $totalAlerts = \App\Models\Alert::whereHas('monitoringTask', function ($query) use ($userId) {
                $query->where('user_id', $userId);
            })->count();

            return [
                'total_tasks' => $totalTasks,
                'active_tasks' => $activeTasks,
                'total_data_sources' => $totalDataSources,
                'products_today' => $productsToday,
                'total_alerts' => $totalAlerts,
            ];
        });
    }
}