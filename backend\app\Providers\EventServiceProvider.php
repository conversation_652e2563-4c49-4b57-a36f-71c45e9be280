<?php

namespace App\Providers;

use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

// 导入认证事件
use Illuminate\Auth\Events\Attempting;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Logout;
use Illuminate\Auth\Events\Failed;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Auth\Events\Verified;

// 导入我们的监听器和观察者
use App\Listeners\AuthEventListener;
use App\Observers\AuditObserver;

// 导入需要审计的模型
use App\Models\User;
use App\Models\DataSource;
use App\Models\MonitoringTask;
use App\Models\TaskGroup;
use App\Models\AlertRule;
use App\Models\ProductData;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        
        // 认证事件监听
        Attempting::class => [
            'App\Listeners\AuthEventListener@handleAttempting',
        ],
        Login::class => [
            'App\Listeners\AuthEventListener@handleLogin',
        ],
        Logout::class => [
            'App\Listeners\AuthEventListener@handleLogout',
        ],
        Failed::class => [
            'App\Listeners\AuthEventListener@handleFailed',
        ],
        Registered::class => [
            'App\Listeners\AuthEventListener@handleRegistered',
        ],
        PasswordReset::class => [
            'App\Listeners\AuthEventListener@handlePasswordReset',
        ],
        Verified::class => [
            'App\Listeners\AuthEventListener@handleVerified',
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        // 注册模型观察者
        User::observe(AuditObserver::class);
        DataSource::observe(AuditObserver::class);
        MonitoringTask::observe(AuditObserver::class);
        TaskGroup::observe(AuditObserver::class);
        AlertRule::observe(AuditObserver::class);
        ProductData::observe(AuditObserver::class);
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
} 