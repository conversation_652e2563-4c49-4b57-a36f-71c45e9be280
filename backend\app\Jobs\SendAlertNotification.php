<?php

namespace App\Jobs;

use App\Models\Alert;
use App\Services\NotificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendAlertNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 预警ID
     */
    private int $alertId;

    /**
     * 任务执行超时时间（秒）
     */
    public int $timeout = 120;

    /**
     * 最大重试次数
     */
    public int $tries = 3;

    /**
     * 创建新的任务实例
     */
    public function __construct(int $alertId)
    {
        $this->alertId = $alertId;
    }

    /**
     * 执行任务
     */
    public function handle(NotificationService $notificationService): void
    {
        Log::info('开始处理预警通知发送任务', [
            'alert_id' => $this->alertId,
        ]);

        try {
            // 获取预警记录
            $alert = Alert::with(['alertRule', 'monitoringTask'])->find($this->alertId);

            if (!$alert) {
                Log::warning('预警记录不存在，跳过通知发送', [
                    'alert_id' => $this->alertId,
                ]);
                return;
            }

            // 检查预警规则是否配置了通知渠道
            $notificationChannels = $alert->alertRule->notification_channels ?? [];
            
            if (empty($notificationChannels)) {
                Log::info('预警规则未配置通知渠道，跳过通知发送', [
                    'alert_id' => $this->alertId,
                    'alert_rule_id' => $alert->alert_rule_id,
                ]);
                return;
            }

            // 发送通知
            $result = $notificationService->sendAlertNotification($alert);

            Log::info('预警通知发送任务完成', [
                'alert_id' => $this->alertId,
                'success' => $result['success'] ?? false,
                'recipients_count' => $result['recipients_count'] ?? 0,
                'sent_count' => $result['sent_count'] ?? 0,
            ]);

        } catch (\Exception $e) {
            Log::error('预警通知发送任务执行失败', [
                'alert_id' => $this->alertId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // 重新抛出异常以触发重试机制
            throw $e;
        }
    }

    /**
     * 任务失败时的处理
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('预警通知发送任务最终失败', [
            'alert_id' => $this->alertId,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);

        // 可以在这里添加失败后的处理逻辑，比如：
        // 1. 记录到失败日志表
        // 2. 发送系统管理员通知
        // 3. 更新预警状态等
    }

    /**
     * 获取任务的唯一标识符
     */
    public function uniqueId(): string
    {
        return "alert_notification_{$this->alertId}";
    }

    /**
     * 获取任务的标签
     */
    public function tags(): array
    {
        return ['alerts', 'notifications', "alert:{$this->alertId}"];
    }
} 