<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('audit_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null')->comment('操作用户ID');
            $table->string('username', 50)->nullable()->comment('用户名快照');
            
            // 操作信息
            $table->string('action', 100)->comment('操作类型');
            $table->string('module', 50)->comment('模块名称');
            $table->string('controller', 100)->nullable()->comment('控制器名称');
            $table->string('method', 100)->nullable()->comment('方法名称');
            $table->text('description')->comment('操作描述');
            
            // 请求信息
            $table->string('request_method', 10)->comment('请求方法：GET,POST,PUT,DELETE等');
            $table->text('request_url')->comment('请求URL');
            $table->json('request_params')->nullable()->comment('请求参数');
            $table->text('request_headers')->nullable()->comment('请求头信息');
            $table->string('user_agent', 500)->nullable()->comment('用户代理');
            $table->string('ip_address', 45)->comment('IP地址');
            $table->string('session_id', 100)->nullable()->comment('会话ID');
            
            // 多态关联
            $table->string('auditable_type', 100)->nullable()->comment('关联模型类型');
            $table->unsignedBigInteger('auditable_id')->nullable()->comment('关联模型ID');
            
            // 数据变更
            $table->json('old_values')->nullable()->comment('变更前数据');
            $table->json('new_values')->nullable()->comment('变更后数据');
            $table->json('changed_fields')->nullable()->comment('变更字段列表');
            
            // 响应信息
            $table->integer('response_code')->nullable()->comment('响应状态码');
            $table->text('response_message')->nullable()->comment('响应消息');
            $table->integer('execution_time')->nullable()->comment('执行时间(毫秒)');
            
            // 风险评估
            $table->string('risk_level', 20)->default('low')->comment('风险级别：low,medium,high,critical');
            $table->tinyInteger('is_suspicious')->default(0)->comment('是否可疑：1-是，0-否');
            $table->text('risk_factors')->nullable()->comment('风险因素');
            
            // 地理位置
            $table->string('country', 50)->nullable()->comment('国家');
            $table->string('region', 50)->nullable()->comment('地区');
            $table->string('city', 50)->nullable()->comment('城市');
            $table->decimal('latitude', 10, 8)->nullable()->comment('纬度');
            $table->decimal('longitude', 11, 8)->nullable()->comment('经度');
            
            $table->timestamps();
            
            // 索引
            $table->index(['user_id', 'created_at']);
            $table->index(['action', 'module']);
            $table->index(['auditable_type', 'auditable_id']);
            $table->index(['ip_address', 'created_at']);
            $table->index(['risk_level', 'is_suspicious']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('audit_logs');
    }
};
