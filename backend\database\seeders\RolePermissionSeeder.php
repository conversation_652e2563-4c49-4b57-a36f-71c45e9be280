<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Role;
use App\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 创建权限
        $permissions = [
            // 用户管理权限
            [
                'name' => 'user.create',
                'display_name' => '创建用户',
                'description' => '创建新用户账户',
                'group' => '用户管理',
                'resource' => 'user',
                'action' => 'create',
            ],
            [
                'name' => 'user.read',
                'display_name' => '查看用户',
                'description' => '查看用户信息和列表',
                'group' => '用户管理',
                'resource' => 'user',
                'action' => 'read',
            ],
            [
                'name' => 'user.update',
                'display_name' => '更新用户',
                'description' => '更新用户信息、状态和密码',
                'group' => '用户管理',
                'resource' => 'user',
                'action' => 'update',
            ],
            [
                'name' => 'user.delete',
                'display_name' => '删除用户',
                'description' => '删除用户账户',
                'group' => '用户管理',
                'resource' => 'user',
                'action' => 'delete',
            ],
            
            // 角色管理权限
            [
                'name' => 'role.create',
                'display_name' => '创建角色',
                'description' => '创建新角色',
                'group' => '角色管理',
                'resource' => 'role',
                'action' => 'create',
            ],
            [
                'name' => 'role.read',
                'display_name' => '查看角色',
                'description' => '查看角色信息和列表',
                'group' => '角色管理',
                'resource' => 'role',
                'action' => 'read',
            ],
            [
                'name' => 'role.update',
                'display_name' => '更新角色',
                'description' => '更新角色信息和权限分配',
                'group' => '角色管理',
                'resource' => 'role',
                'action' => 'update',
            ],
            [
                'name' => 'role.delete',
                'display_name' => '删除角色',
                'description' => '删除角色',
                'group' => '角色管理',
                'resource' => 'role',
                'action' => 'delete',
            ],
            
            // 权限管理权限
            [
                'name' => 'permission.create',
                'display_name' => '创建权限',
                'description' => '创建新权限',
                'group' => '权限管理',
                'resource' => 'permission',
                'action' => 'create',
            ],
            [
                'name' => 'permission.read',
                'display_name' => '查看权限',
                'description' => '查看权限信息和列表',
                'group' => '权限管理',
                'resource' => 'permission',
                'action' => 'read',
            ],
            [
                'name' => 'permission.update',
                'display_name' => '更新权限',
                'description' => '更新权限信息',
                'group' => '权限管理',
                'resource' => 'permission',
                'action' => 'update',
            ],
            [
                'name' => 'permission.delete',
                'display_name' => '删除权限',
                'description' => '删除权限',
                'group' => '权限管理',
                'resource' => 'permission',
                'action' => 'delete',
            ],
            
            // 系统管理权限
            [
                'name' => 'system.config',
                'display_name' => '系统配置',
                'description' => '系统配置管理',
                'group' => '系统管理',
                'resource' => 'system',
                'action' => 'config',
            ],
            [
                'name' => 'system.log',
                'display_name' => '系统日志',
                'description' => '查看系统日志',
                'group' => '系统管理',
                'resource' => 'system',
                'action' => 'log',
            ],
            
            // 数据源管理权限
            [
                'name' => 'data_source.create',
                'display_name' => '创建数据源',
                'description' => '创建新数据源',
                'group' => '数据源管理',
                'resource' => 'data_source',
                'action' => 'create',
            ],
            [
                'name' => 'data_source.read',
                'display_name' => '查看数据源',
                'description' => '查看数据源信息',
                'group' => '数据源管理',
                'resource' => 'data_source',
                'action' => 'read',
            ],
            [
                'name' => 'data_source.update',
                'display_name' => '更新数据源',
                'description' => '更新数据源配置',
                'group' => '数据源管理',
                'resource' => 'data_source',
                'action' => 'update',
            ],
            [
                'name' => 'data_source.delete',
                'display_name' => '删除数据源',
                'description' => '删除数据源',
                'group' => '数据源管理',
                'resource' => 'data_source',
                'action' => 'delete',
            ],
            
            // 监控任务权限
            [
                'name' => 'monitoring_task.create',
                'display_name' => '创建监控任务',
                'description' => '创建新监控任务',
                'group' => '监控管理',
                'resource' => 'monitoring_task',
                'action' => 'create',
            ],
            [
                'name' => 'monitoring_task.read',
                'display_name' => '查看监控任务',
                'description' => '查看监控任务信息',
                'group' => '监控管理',
                'resource' => 'monitoring_task',
                'action' => 'read',
            ],
            [
                'name' => 'monitoring_task.update',
                'display_name' => '更新监控任务',
                'description' => '更新监控任务配置',
                'group' => '监控管理',
                'resource' => 'monitoring_task',
                'action' => 'update',
            ],
            [
                'name' => 'monitoring_task.delete',
                'display_name' => '删除监控任务',
                'description' => '删除监控任务',
                'group' => '监控管理',
                'resource' => 'monitoring_task',
                'action' => 'delete',
            ],
            
            // 任务组权限
            [
                'name' => 'task_group.create',
                'display_name' => '创建任务组',
                'description' => '创建新任务组',
                'group' => '任务组管理',
                'resource' => 'task_group',
                'action' => 'create',
            ],
            [
                'name' => 'task_group.read',
                'display_name' => '查看任务组',
                'description' => '查看任务组信息',
                'group' => '任务组管理',
                'resource' => 'task_group',
                'action' => 'read',
            ],
            [
                'name' => 'task_group.update',
                'display_name' => '更新任务组',
                'description' => '更新任务组配置',
                'group' => '任务组管理',
                'resource' => 'task_group',
                'action' => 'update',
            ],
            [
                'name' => 'task_group.delete',
                'display_name' => '删除任务组',
                'description' => '删除任务组',
                'group' => '任务组管理',
                'resource' => 'task_group',
                'action' => 'delete',
            ],
        ];
        
        // 创建权限
        foreach ($permissions as $permissionData) {
            Permission::firstOrCreate(
                ['name' => $permissionData['name']],
                array_merge($permissionData, ['status' => 1, 'sort_order' => 0])
            );
        }
        
        // 创建角色
        $roles = [
            [
                'name' => 'super_admin',
                'display_name' => '超级管理员',
                'description' => '拥有所有权限的超级管理员',
                'status' => 1,
                'sort_order' => 0,
            ],
            [
                'name' => 'admin',
                'display_name' => '管理员',
                'description' => '系统管理员，拥有大部分权限',
                'status' => 1,
                'sort_order' => 1,
            ],
            [
                'name' => 'operator',
                'display_name' => '操作员',
                'description' => '系统操作员，可以管理监控任务和数据源',
                'status' => 1,
                'sort_order' => 2,
            ],
            [
                'name' => 'viewer',
                'display_name' => '观察者',
                'description' => '只能查看数据，无法修改',
                'status' => 1,
                'sort_order' => 3,
            ],
        ];
        
        foreach ($roles as $roleData) {
            Role::firstOrCreate(
                ['name' => $roleData['name']],
                $roleData
            );
        }
        
        // 为角色分配权限
        $this->assignPermissionsToRoles();
        
        // 创建默认超级管理员用户
        $this->createDefaultSuperAdmin();
    }
    
    /**
     * 为角色分配权限
     */
    private function assignPermissionsToRoles(): void
    {
        // 超级管理员拥有所有权限
        $superAdmin = Role::where('name', 'super_admin')->first();
        $allPermissions = Permission::all();
        $superAdmin->permissions()->sync($allPermissions->pluck('id')->toArray());
        
        // 管理员权限（除了超级管理员专有权限）
        $admin = Role::where('name', 'admin')->first();
        $adminPermissions = Permission::whereIn('name', [
            'user.create', 'user.read', 'user.update', 'user.delete',
            'role.read', 'role.update',
            'permission.read',
            'data_source.create', 'data_source.read', 'data_source.update', 'data_source.delete',
            'monitoring_task.create', 'monitoring_task.read', 'monitoring_task.update', 'monitoring_task.delete',
            'task_group.create', 'task_group.read', 'task_group.update', 'task_group.delete',
            'system.log',
        ])->get();
        $admin->permissions()->sync($adminPermissions->pluck('id')->toArray());
        
        // 操作员权限
        $operator = Role::where('name', 'operator')->first();
        $operatorPermissions = Permission::whereIn('name', [
            'data_source.create', 'data_source.read', 'data_source.update',
            'monitoring_task.create', 'monitoring_task.read', 'monitoring_task.update',
            'task_group.create', 'task_group.read', 'task_group.update',
        ])->get();
        $operator->permissions()->sync($operatorPermissions->pluck('id')->toArray());
        
        // 观察者权限
        $viewer = Role::where('name', 'viewer')->first();
        $viewerPermissions = Permission::whereIn('name', [
            'data_source.read',
            'monitoring_task.read',
            'task_group.read',
        ])->get();
        $viewer->permissions()->sync($viewerPermissions->pluck('id')->toArray());
    }
    
    /**
     * 创建默认超级管理员用户
     */
    private function createDefaultSuperAdmin(): void
    {
        $user = User::firstOrCreate(
            ['username' => 'admin'],
            [
                'name' => '超级管理员',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'real_name' => '系统管理员',
                'status' => 1,
            ]
        );
        
                 // 分配超级管理员角色
         $superAdminRole = Role::where('name', 'super_admin')->first();
         if ($superAdminRole && !$user->hasRole('super_admin')) {
             $user->roles()->attach($superAdminRole->id, [
                 'assigned_at' => now(),
                 'assigned_by' => 1,
             ]);
         }
     }
 }
 