<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ProductData;
use App\Models\MonitoringTask;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class ProductDataController extends Controller
{
    /**
     * 获取产品数据列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = ProductData::with(['monitoringTask.dataSource'])
            ->whereHas('monitoringTask', function ($q) {
                $q->where('user_id', Auth::id());
            });
        
        // 按任务筛选
        if ($request->has('task_id')) {
            $taskId = $request->get('task_id');
            $task = MonitoringTask::where('id', $taskId)
                ->where('user_id', Auth::id())
                ->first();
            
            if ($task) {
                $query->where('monitoring_task_id', $task->id);
                
                // 如果任务指定了目标产品，只显示这些产品
                if (!empty($task->target_products)) {
                    $query->whereIn('item_id', $task->target_products);
                }
            }
        }
        
        // 按数据源筛选 - 通过监控任务关联查询
        if ($request->has('data_source_id')) {
            $dataSourceId = $request->get('data_source_id');
            $query->whereHas('monitoringTask', function ($q) use ($dataSourceId) {
                $q->where('data_source_id', $dataSourceId);
            });
        }
        
        // 按商品ID筛选
        if ($request->has('item_id')) {
            $query->where('item_id', 'like', '%' . $request->get('item_id') . '%');
        }
        
        // 按时间范围筛选
        if ($request->has('date_from')) {
            $query->where('last_collected_at', '>=', $request->get('date_from'));
        }
        
        if ($request->has('date_to')) {
            $query->where('last_collected_at', '<=', $request->get('date_to'));
        }
        
        // 搜索功能
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('item_id', 'like', "%{$search}%")
                  ->orWhereRaw('JSON_EXTRACT(standardized_data, "$.title") LIKE ?', ["%{$search}%"])
                  ->orWhereRaw('JSON_EXTRACT(standardized_data, "$.name") LIKE ?', ["%{$search}%"]);
            });
        }
        
        // 排序
        $sortBy = $request->get('sort_by', 'last_collected_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        if ($sortBy === 'price') {
            $query->orderByRaw('CAST(JSON_EXTRACT(standardized_data, "$.price") AS DECIMAL(10,2)) ' . $sortOrder);
        } elseif ($sortBy === 'title') {
            $query->orderByRaw('JSON_EXTRACT(standardized_data, "$.title") ' . $sortOrder);
        } else {
            $query->orderBy($sortBy, $sortOrder);
        }
        
        // 分页
        $perPage = $request->get('per_page', 20);
        $products = $query->paginate($perPage);
        
        // 格式化数据
        $products->getCollection()->transform(function ($product) {
            $standardized = $product->standardized_data ?? [];
            
            return [
                'id' => $product->id,
                'item_id' => $product->item_id,
                'data_source' => $product->monitoringTask?->dataSource?->name ?? '未知数据源',
                'data_source_id' => $product->monitoringTask?->data_source_id,
                'title' => $standardized['title'] ?? $standardized['name'] ?? '未知商品',
                'price' => $standardized['price'] ?? $standardized['subPrice'] ?? 0,
                'original_price' => $standardized['original_price'] ?? null,
                'currency' => $standardized['currency'] ?? 'CNY',
                'quantity' => $standardized['quantity'] ?? $standardized['Quantity'] ?? 0,
                'status' => $standardized['status'] ?? $standardized['State'] ?? '未知',
                'url' => $standardized['url'] ?? null,
                'image' => $standardized['image'] ?? $standardized['imageUrl'] ?? null,
                'description' => $standardized['description'] ?? null,
                'category' => $standardized['category'] ?? null,
                'brand' => $standardized['brand'] ?? null,
                'last_collected_at' => $product->last_collected_at,
                'raw_data' => $product->raw_data,
                'standardized_data' => $product->standardized_data,
            ];
        });
        
        return response()->json([
            'success' => true,
            'data' => $products,
            'message' => '产品数据获取成功'
        ]);
    }
    
    /**
     * 获取单个产品数据详情
     */
    public function show(ProductData $productData): JsonResponse
    {
        // 验证用户权限
        $hasAccess = MonitoringTask::where('user_id', Auth::id())
            ->where('id', $productData->monitoring_task_id)
            ->exists();
        
        if (!$hasAccess) {
            return response()->json([
                'success' => false,
                'message' => '无权访问该产品数据'
            ], 403);
        }
        
        $productData->load('monitoringTask.dataSource');
        
        return response()->json([
            'success' => true,
            'data' => $productData,
            'message' => '产品数据详情获取成功'
        ]);
    }
    
    /**
     * 批量获取产品数据对比
     */
    public function compare(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'product_ids' => 'required|array|min:2|max:10',
            'product_ids.*' => 'integer|exists:product_data,id'
        ]);
        
        $products = ProductData::with(['monitoringTask.dataSource'])
            ->whereIn('id', $validated['product_ids'])
            ->whereHas('monitoringTask', function ($q) {
                $q->where('user_id', Auth::id());
            })
            ->get();
        
        if ($products->count() !== count($validated['product_ids'])) {
            return response()->json([
                'success' => false,
                'message' => '部分产品数据无权访问或不存在'
            ], 403);
        }
        
        // 格式化对比数据
        $compareData = $products->map(function ($product) {
            $standardized = $product->standardized_data ?? [];
            
            return [
                'id' => $product->id,
                'item_id' => $product->item_id,
                'data_source' => $product->monitoringTask?->dataSource?->name,
                'title' => $standardized['title'] ?? $standardized['name'] ?? '未知商品',
                'price' => $standardized['price'] ?? $standardized['subPrice'] ?? 0,
                'original_price' => $standardized['original_price'] ?? null,
                'currency' => $standardized['currency'] ?? 'CNY',
                'quantity' => $standardized['quantity'] ?? $standardized['Quantity'] ?? 0,
                'status' => $standardized['status'] ?? $standardized['State'] ?? '未知',
                'url' => $standardized['url'] ?? null,
                'image' => $standardized['image'] ?? $standardized['imageUrl'] ?? null,
                'description' => $standardized['description'] ?? null,
                'category' => $standardized['category'] ?? null,
                'brand' => $standardized['brand'] ?? null,
                'last_collected_at' => $product->last_collected_at,
            ];
        });
        
        return response()->json([
            'success' => true,
            'data' => $compareData,
            'message' => '产品对比数据获取成功'
        ]);
    }
    
    /**
     * 获取产品价格历史趋势
     */
    public function priceHistory(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'item_id' => 'required|string',
            'data_source_id' => 'required|integer|exists:data_sources,id',
            'days' => 'integer|min:1|max:365'
        ]);
        
        // 验证用户权限
        $hasAccess = MonitoringTask::where('user_id', Auth::id())
            ->where('data_source_id', $validated['data_source_id'])
            ->exists();
        
        if (!$hasAccess) {
            return response()->json([
                'success' => false,
                'message' => '无权访问该数据源的产品数据'
            ], 403);
        }
        
        $days = $validated['days'] ?? 30;
        $startDate = now()->subDays($days);
        
        $priceHistory = ProductData::where('item_id', $validated['item_id'])
            ->whereHas('monitoringTask', function ($q) use ($validated) {
                $q->where('data_source_id', $validated['data_source_id']);
            })
            ->where('last_collected_at', '>=', $startDate)
            ->orderBy('last_collected_at', 'asc')
            ->get()
            ->map(function ($product) {
                $standardized = $product->standardized_data ?? [];
                return [
                    'date' => $product->last_collected_at->format('Y-m-d H:i:s'),
                    'price' => $standardized['price'] ?? $standardized['subPrice'] ?? 0,
                    'original_price' => $standardized['original_price'] ?? null,
                    'quantity' => $standardized['quantity'] ?? $standardized['Quantity'] ?? 0,
                    'status' => $standardized['status'] ?? $standardized['State'] ?? '未知',
                ];
            });
        
        return response()->json([
            'success' => true,
            'data' => $priceHistory,
            'message' => '价格历史数据获取成功'
        ]);
    }
    
    /**
     * 获取数据统计信息
     */
    public function statistics(Request $request): JsonResponse
    {
        $query = ProductData::whereHas('monitoringTask', function ($q) {
            $q->where('user_id', Auth::id());
        });
        
        // 按任务筛选
        if ($request->has('task_id')) {
            $taskId = $request->get('task_id');
            $task = MonitoringTask::where('id', $taskId)
                ->where('user_id', Auth::id())
                ->first();
            
            if ($task) {
                $query->where('monitoring_task_id', $task->id);
                if (!empty($task->target_products)) {
                    $query->whereIn('item_id', $task->target_products);
                }
            }
        }
        
        $totalProducts = $query->count();
        $recentProducts = $query->where('last_collected_at', '>=', now()->subDays(7))->count();
        
        // 按数据源统计
        $byDataSource = $query->with('monitoringTask.dataSource')
            ->get()
            ->groupBy(function ($item) {
                return $item->monitoringTask?->dataSource?->id ?? 0;
            })
            ->map(function ($items, $dataSourceId) {
                $firstItem = $items->first();
                return [
                    'data_source' => $firstItem->monitoringTask?->dataSource?->name ?? '未知数据源',
                    'count' => $items->count()
                ];
            })
            ->values();
        
        // 价格区间统计
        $priceRanges = [
            '0-100' => 0,
            '100-500' => 0,
            '500-1000' => 0,
            '1000-5000' => 0,
            '5000+' => 0
        ];
        
        $products = $query->get();
        foreach ($products as $product) {
            $standardized = $product->standardized_data ?? [];
            $price = $standardized['price'] ?? $standardized['subPrice'] ?? 0;
            
            if ($price <= 100) {
                $priceRanges['0-100']++;
            } elseif ($price <= 500) {
                $priceRanges['100-500']++;
            } elseif ($price <= 1000) {
                $priceRanges['500-1000']++;
            } elseif ($price <= 5000) {
                $priceRanges['1000-5000']++;
            } else {
                $priceRanges['5000+']++;
            }
        }
        
        return response()->json([
            'success' => true,
            'data' => [
                'total_products' => $totalProducts,
                'recent_products' => $recentProducts,
                'by_data_source' => $byDataSource,
                'price_ranges' => $priceRanges,
            ],
            'message' => '统计信息获取成功'
        ]);
    }
} 