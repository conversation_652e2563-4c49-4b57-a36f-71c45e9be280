<?php

namespace App\Services;

use App\Models\Alert;
use App\Models\User;
use App\Notifications\AlertTriggeredNotification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class NotificationService
{
    /**
     * 发送预警通知
     *
     * @param Alert $alert
     * @return array
     */
    public function sendAlertNotification(Alert $alert): array
    {
        Log::info('开始发送预警通知', [
            'alert_id' => $alert->id,
            'alert_rule_id' => $alert->alert_rule_id,
        ]);

        try {
            // 加载预警规则和监控任务关系
            $alert->load(['alertRule', 'monitoringTask']);
            
            if (!$alert->alertRule) {
                throw new \Exception('预警规则不存在');
            }

            // 获取通知接收者
            $recipients = $this->getNotificationRecipients($alert);
            
            if (empty($recipients)) {
                Log::warning('没有找到通知接收者', ['alert_id' => $alert->id]);
                return [
                    'success' => true,
                    'message' => '没有配置通知接收者',
                    'recipients_count' => 0,
                ];
            }

            // 创建通知实例
            $notification = new AlertTriggeredNotification($alert);

            // 发送通知
            $sentCount = 0;
            $errors = [];

            foreach ($recipients as $recipient) {
                try {
                    $recipient->notify($notification);
                    $sentCount++;
                    
                    Log::debug('通知发送成功', [
                        'alert_id' => $alert->id,
                        'recipient_id' => $recipient->id,
                        'recipient_email' => $recipient->email,
                    ]);
                } catch (\Exception $e) {
                    $errors[] = [
                        'recipient_id' => $recipient->id,
                        'error' => $e->getMessage(),
                    ];
                    
                    Log::error('发送通知失败', [
                        'alert_id' => $alert->id,
                        'recipient_id' => $recipient->id,
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            // 更新预警规则的通知统计
            $this->updateNotificationStatistics($alert->alertRule);

            $result = [
                'success' => true,
                'recipients_count' => count($recipients),
                'sent_count' => $sentCount,
                'failed_count' => count($errors),
            ];

            if (!empty($errors)) {
                $result['errors'] = $errors;
            }

            Log::info('预警通知发送完成', array_merge(['alert_id' => $alert->id], $result));

            return $result;

        } catch (\Exception $e) {
            Log::error('发送预警通知失败', [
                'alert_id' => $alert->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'recipients_count' => 0,
                'sent_count' => 0,
            ];
        }
    }

    /**
     * 获取通知接收者列表
     *
     * @param Alert $alert
     * @return array
     */
    private function getNotificationRecipients(Alert $alert): array
    {
        $recipients = [];
        $alertRule = $alert->alertRule;
        $monitoringTask = $alert->monitoringTask;

        // 1. 从预警规则的recipients字段获取接收者
        $ruleRecipients = $alertRule->recipients ?? [];
        
        if (!empty($ruleRecipients)) {
            // 支持邮箱地址和用户ID两种格式
            foreach ($ruleRecipients as $recipient) {
                if (is_numeric($recipient)) {
                    // 用户ID
                    $user = User::find($recipient);
                    if ($user) {
                        $recipients[] = $user;
                    }
                } elseif (filter_var($recipient, FILTER_VALIDATE_EMAIL)) {
                    // 邮箱地址，查找对应用户
                    $user = User::where('email', $recipient)->first();
                    if ($user) {
                        $recipients[] = $user;
                    }
                }
            }
        }

        // 2. 如果没有指定接收者，默认发送给任务创建者
        if (empty($recipients) && $monitoringTask && $monitoringTask->user) {
            $recipients[] = $monitoringTask->user;
        }

        // 3. 去重（基于用户ID）
        $uniqueRecipients = [];
        $userIds = [];
        
        foreach ($recipients as $recipient) {
            if (!in_array($recipient->id, $userIds)) {
                $uniqueRecipients[] = $recipient;
                $userIds[] = $recipient->id;
            }
        }

        return $uniqueRecipients;
    }

    /**
     * 更新预警规则的通知统计
     *
     * @param \App\Models\AlertRule $alertRule
     * @return void
     */
    private function updateNotificationStatistics($alertRule): void
    {
        try {
            $alertRule->update([
                'last_notification_at' => now(),
            ]);
        } catch (\Exception $e) {
            Log::warning('更新预警规则通知统计失败', [
                'alert_rule_id' => $alertRule->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * 批量发送预警通知
     *
     * @param array $alertIds
     * @return array
     */
    public function sendBatchAlertNotifications(array $alertIds): array
    {
        Log::info('开始批量发送预警通知', [
            'alert_ids' => $alertIds,
            'count' => count($alertIds),
        ]);

        $results = [];
        $totalSent = 0;
        $totalFailed = 0;

        foreach ($alertIds as $alertId) {
            $alert = Alert::find($alertId);
            
            if (!$alert) {
                $results[$alertId] = [
                    'success' => false,
                    'error' => '预警记录不存在',
                ];
                $totalFailed++;
                continue;
            }

            $result = $this->sendAlertNotification($alert);
            $results[$alertId] = $result;

            if ($result['success']) {
                $totalSent += $result['sent_count'] ?? 0;
            } else {
                $totalFailed++;
            }
        }

        Log::info('批量发送预警通知完成', [
            'total_alerts' => count($alertIds),
            'total_sent' => $totalSent,
            'total_failed' => $totalFailed,
        ]);

        return [
            'success' => true,
            'total_alerts' => count($alertIds),
            'total_sent' => $totalSent,
            'total_failed' => $totalFailed,
            'results' => $results,
        ];
    }

    /**
     * 发送测试通知
     *
     * @param User $user
     * @param string $message
     * @return array
     */
    public function sendTestNotification(User $user, string $message = '这是一条测试通知'): array
    {
        try {
            // 创建一个测试通知
            Notification::send($user, new class($message) extends \Illuminate\Notifications\Notification {
                private $message;
                
                public function __construct($message) {
                    $this->message = $message;
                }
                
                public function via($notifiable) {
                    return ['mail', 'database'];
                }
                
                public function toMail($notifiable) {
                    return (new \Illuminate\Notifications\Messages\MailMessage)
                        ->subject('测试通知')
                        ->line($this->message)
                        ->line('这是来自电商监控系统的测试通知。');
                }
                
                public function toArray($notifiable) {
                    return [
                        'message' => $this->message,
                        'type' => 'test',
                        'sent_at' => now()->toISOString(),
                    ];
                }
            });

            return [
                'success' => true,
                'message' => '测试通知发送成功',
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }
} 