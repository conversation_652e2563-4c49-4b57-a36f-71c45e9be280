<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\MonitoringTask;
use App\Models\DataSource;
use App\Models\TaskGroup;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\StoreMonitoringTaskRequest;
use App\Http\Requests\UpdateMonitoringTaskRequest;

class MonitoringTaskController extends Controller
{
    /**
     * 获取监控任务列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = MonitoringTask::with(['user', 'dataSource', 'taskGroup'])
            ->where('user_id', Auth::id());
        
        // 支持搜索
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }
        
        // 按任务组筛选
        if ($request->has('task_group_id')) {
            $query->where('task_group_id', $request->get('task_group_id'));
        }
        
        // 按数据源筛选
        if ($request->has('data_source_id')) {
            $query->where('data_source_id', $request->get('data_source_id'));
        }
        
        // 按状态筛选
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }
        
        // 按频率类型筛选
        if ($request->has('frequency_type')) {
            $query->where('frequency_type', $request->get('frequency_type'));
        }
        
        // 排序
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);
        
        // 分页
        $perPage = $request->get('per_page', 15);
        $tasks = $query->paginate($perPage);
        
        return response()->json([
            'success' => true,
            'data' => $tasks,
            'message' => '监控任务列表获取成功'
        ]);
    }
    
    /**
     * 获取任务状态列表
     */
    public function statuses(): JsonResponse
    {
        $statuses = MonitoringTask::getAvailableStatuses();
        
        return response()->json([
            'success' => true,
            'data' => $statuses,
            'message' => '任务状态列表获取成功'
        ]);
    }
    
    /**
     * 获取频率类型列表
     */
    public function frequencyTypes(): JsonResponse
    {
        $types = MonitoringTask::getFrequencyTypes();
        
        return response()->json([
            'success' => true,
            'data' => $types,
            'message' => '频率类型列表获取成功'
        ]);
    }
    
    /**
     * 获取单个监控任务详情
     */
    public function show(MonitoringTask $monitoringTask): JsonResponse
    {
        $this->authorize('view', $monitoringTask);
        
        $monitoringTask->load(['user', 'dataSource', 'taskGroup', 'alerts']);
        
        return response()->json([
            'success' => true,
            'data' => $monitoringTask,
            'message' => '监控任务详情获取成功'
        ]);
    }
    
    /**
     * 创建新监控任务
     */
    public function store(StoreMonitoringTaskRequest $request): JsonResponse
    {
        $validated = $request->validated();
        
        $task = MonitoringTask::create([
            'name' => $validated['name'],
            'description' => $validated['description'] ?? '',
            'user_id' => Auth::id(),
            'task_group_id' => $validated['task_group_id'] ?? null,
            'data_source_id' => $validated['data_source_id'],
            'target_products' => $validated['target_products'] ?? [],
            'monitor_fields' => $validated['monitor_fields'] ?? [],
            'frequency_type' => $validated['frequency_type'],
            'frequency_value' => $validated['frequency_value'] ?? null,
            'cron_expression' => $validated['cron_expression'] ?? null,
            'status' => 'pending',
            'auto_start' => $validated['auto_start'] ?? false,
            'notify_on_error' => $validated['notify_on_error'] ?? true,
            'notification_config' => $validated['notification_config'] ?? [],
            'run_count' => 0,
            'success_count' => 0,
            'failed_count' => 0,
            'total_products' => 0,
            'active_products' => 0,
            'alert_count' => 0,
        ]);
        
        $task->load(['user', 'dataSource', 'taskGroup']);
        
        return response()->json([
            'success' => true,
            'data' => $task,
            'message' => '监控任务创建成功'
        ], 201);
    }
    
    /**
     * 更新监控任务
     */
    public function update(UpdateMonitoringTaskRequest $request, MonitoringTask $monitoringTask): JsonResponse
    {
        $validated = $request->validated();
        
        $monitoringTask->update($validated);
        
        $monitoringTask->load(['user', 'dataSource', 'taskGroup']);

        return response()->json([
            'success' => true,
            'data' => $monitoringTask,
            'message' => '监控任务更新成功'
        ]);
    }
    
    /**
     * 删除监控任务
     */
    public function destroy(MonitoringTask $monitoringTask): JsonResponse
    {
        $this->authorize('delete', $monitoringTask);

        $monitoringTask->delete();

        return response()->json([
            'success' => true,
            'message' => '监控任务删除成功'
        ]);
    }
    
    /**
     * 批量删除监控任务
     */
    public function batchDestroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => 'required|array|min:1',
            'ids.*' => 'integer|exists:monitoring_tasks,id',
        ]);
        
        $tasks = MonitoringTask::whereIn('id', $validated['ids'])
            ->where('user_id', Auth::id())
            ->get();
        
        if ($tasks->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => '没有找到可删除的监控任务'
            ], 404);
        }
        
        // 检查是否有正在运行的任务
        $runningTasks = $tasks->filter(function ($task) {
            return $task->isRunning();
        });
        
        if ($runningTasks->isNotEmpty()) {
            return response()->json([
                'success' => false,
                'message' => '无法删除正在运行的任务，请先停止这些任务',
                'running_tasks' => $runningTasks->pluck('name')
            ], 422);
        }
        
        $deletedCount = $tasks->count();
        MonitoringTask::whereIn('id', $tasks->pluck('id'))->delete();
        
        return response()->json([
            'success' => true,
            'message' => "成功删除 {$deletedCount} 个监控任务"
        ]);
    }
    
    /**
     * 更新任务状态
     */
    public function updateStatus(Request $request, MonitoringTask $monitoringTask): JsonResponse
    {
        // 确保用户只能更新自己的任务
        if ($monitoringTask->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权修改该监控任务状态'
            ], 403);
        }
        
        $validated = $request->validate([
            'status' => 'required|in:pending,running,paused,stopped,failed',
        ]);
        
        $monitoringTask->update([
            'status' => $validated['status']
        ]);
        
        return response()->json([
            'success' => true,
            'data' => $monitoringTask,
            'message' => '任务状态更新成功'
        ]);
    }
    
    /**
     * 获取任务统计信息
     */
    public function statistics(MonitoringTask $monitoringTask): JsonResponse
    {
        // 确保用户只能查看自己的任务
        if ($monitoringTask->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权查看该监控任务统计'
            ], 403);
        }
        
        $statistics = [
            'basic_info' => [
                'id' => $monitoringTask->id,
                'name' => $monitoringTask->name,
                'status' => $monitoringTask->status,
                'created_at' => $monitoringTask->created_at,
            ],
            'execution_stats' => [
                'run_count' => $monitoringTask->run_count,
                'success_count' => $monitoringTask->success_count,
                'failed_count' => $monitoringTask->failed_count,
                'success_rate' => $monitoringTask->success_rate,
                'last_run_at' => $monitoringTask->last_run_at,
                'next_run_at' => $monitoringTask->next_run_at,
            ],
            'product_stats' => [
                'total_products' => $monitoringTask->total_products,
                'active_products' => $monitoringTask->active_products,
                'alert_count' => $monitoringTask->alert_count,
            ],
            'recent_alerts' => $monitoringTask->alerts()->latest()->limit(5)->get(),
        ];
        
        return response()->json([
            'success' => true,
            'data' => $statistics,
            'message' => '任务统计信息获取成功'
        ]);
    }
    
    /**
     * 复制监控任务
     */
    public function duplicate(MonitoringTask $monitoringTask): JsonResponse
    {
        // 确保用户只能复制自己的任务
        if ($monitoringTask->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权复制该监控任务'
            ], 403);
        }
        
        $newTask = $monitoringTask->replicate();
        $newTask->name = $monitoringTask->name . ' (副本)';
        $newTask->status = 'pending';
        $newTask->run_count = 0;
        $newTask->success_count = 0;
        $newTask->failed_count = 0;
        $newTask->last_run_at = null;
        $newTask->next_run_at = null;
        $newTask->last_error = null;
        $newTask->total_products = 0;
        $newTask->active_products = 0;
        $newTask->alert_count = 0;
        $newTask->save();
        
        $newTask->load(['user', 'dataSource', 'taskGroup']);
        
        return response()->json([
            'success' => true,
            'data' => $newTask,
            'message' => '监控任务复制成功'
        ], 201);
    }

    /**
     * 获取所有监控任务的简要列表
     */
    public function all(): JsonResponse
    {
        $tasks = MonitoringTask::where('user_id', Auth::id())
            ->where('status', '!=', 'archived') // 假设有归档状态
            ->select('id', 'name', 'status')
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $tasks,
            'message' => '所有监控任务列表获取成功'
        ]);
    }
} 