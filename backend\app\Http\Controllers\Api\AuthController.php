<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    /**
     * 用户注册
     */
    public function register(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string|max:50|unique:users',
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:100|unique:users',
            'password' => 'required|string|min:6|confirmed',
            'real_name' => 'nullable|string|max:50',
            'phone' => 'nullable|string|max:20',
        ], [
            'username.required' => '用户名不能为空',
            'username.unique' => '用户名已存在',
            'name.required' => '姓名不能为空',
            'email.required' => '邮箱不能为空',
            'email.email' => '邮箱格式不正确',
            'email.unique' => '邮箱已存在',
            'password.required' => '密码不能为空',
            'password.min' => '密码至少6位',
            'password.confirmed' => '密码确认不一致',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = User::create([
                'username' => $request->username,
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'real_name' => $request->real_name,
                'phone' => $request->phone,
                'status' => 1,
            ]);

            $token = $user->createToken('auth-token')->plainTextToken;

            return response()->json([
                'success' => true,
                'message' => '注册成功',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'username' => $user->username,
                        'name' => $user->name,
                        'email' => $user->email,
                        'real_name' => $user->real_name,
                        'phone' => $user->phone,
                        'status' => $user->status,
                    ],
                    'token' => $token,
                    'token_type' => 'Bearer'
                ]
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '注册失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 用户登录
     */
    public function login(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'login' => 'required|string', // 可以是用户名或邮箱
            'password' => 'required|string',
        ], [
            'login.required' => '用户名或邮箱不能为空',
            'password.required' => '密码不能为空',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // 检查是邮箱还是用户名
            $loginField = filter_var($request->login, FILTER_VALIDATE_EMAIL) ? 'email' : 'username';
            
            $user = User::where($loginField, $request->login)
                       ->where('status', 1)
                       ->first();

            if (!$user || !Hash::check($request->password, $user->password)) {
                return response()->json([
                    'success' => false,
                    'message' => '用户名/邮箱或密码错误'
                ], 401);
            }

            // 更新最后登录信息
            $user->update([
                'last_login_at' => now(),
                'last_login_ip' => $request->ip()
            ]);

            $token = $user->createToken('auth-token')->plainTextToken;

            return response()->json([
                'success' => true,
                'message' => '登录成功',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'username' => $user->username,
                        'name' => $user->name,
                        'email' => $user->email,
                        'real_name' => $user->real_name,
                        'phone' => $user->phone,
                        'status' => $user->status,
                        'last_login_at' => $user->last_login_at,
                    ],
                    'token' => $token,
                    'token_type' => 'Bearer'
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '登录失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 用户登出
     */
    public function logout(Request $request): JsonResponse
    {
        try {
            // 删除当前访问token
            $request->user()->currentAccessToken()->delete();

            return response()->json([
                'success' => true,
                'message' => '登出成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '登出失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取当前用户信息
     */
    public function me(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $user->load('roles.permissions');

            return response()->json([
                'success' => true,
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'username' => $user->username,
                        'name' => $user->name,
                        'email' => $user->email,
                        'real_name' => $user->real_name,
                        'phone' => $user->phone,
                        'status' => $user->status,
                        'last_login_at' => $user->last_login_at,
                        'last_login_ip' => $user->last_login_ip,
                        'created_at' => $user->created_at,
                    ],
                    'roles' => $user->roles->map(function ($role) {
                        return [
                            'id' => $role->id,
                            'name' => $role->name,
                            'display_name' => $role->display_name,
                            'permissions' => $role->permissions->pluck('name')->toArray()
                        ];
                    }),
                    'permissions' => $user->getAllPermissions()->pluck('name')->toArray()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取用户信息失败：' . $e->getMessage()
            ], 500);
        }
    }
}
