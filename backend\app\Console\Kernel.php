<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // 每分钟检查并调度到期的监控任务
        $schedule->command('schedule:dispatch-tasks')
                 ->everyMinute()
                 ->withoutOverlapping()
                 ->runInBackground();

        // 每小时清理过期的队列任务
        $schedule->command('queue:prune-batches --hours=48')
                 ->hourly();

        // 每天清理日志文件
        $schedule->command('queue:prune-failed --hours=168')
                 ->daily();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
} 