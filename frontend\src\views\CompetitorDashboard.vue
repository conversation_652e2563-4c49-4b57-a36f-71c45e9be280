<template>
  <div class="competitor-dashboard p-4">
    <el-card shadow="never" class="mb-4">
      <div class="filter-container flex flex-wrap items-center gap-4">
        <span class="font-bold">筛选条件:</span>
        <el-date-picker
          v-model="filters.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :shortcuts="dateShortcuts"
          value-format="YYYY-MM-DD"
          class="w-64"
        />
        <el-select v-model="filters.categoryId" placeholder="选择产品类目" clearable class="w-48">
          <el-option v-for="item in categoryOptions" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <el-input v-model="filters.keyword" placeholder="产品关键词" clearable class="w-48" @keyup.enter="fetchData" />
        <el-button type="primary" :icon="Search" @click="fetchData" :loading="loading.main">查询</el-button>
        <el-button :icon="Refresh" @click="resetFilters">重置</el-button>
      </div>
    </el-card>

    <el-row :gutter="20">
      <el-col :span="24">
        <el-row :gutter="20" class="mb-5">
          <el-col :xs="24" :sm="12" :md="6" v-for="metric in keyMetrics" :key="metric.title">
            <el-card shadow="hover">
              <div class="text-gray-500">{{ metric.title }}</div>
              <div class="text-2xl font-bold my-2">{{ metric.value }}</div>
              <div class="text-sm" :class="metric.change >= 0 ? 'text-green-500' : 'text-red-500'">
                <el-icon><component :is="metric.change >= 0 ? CaretTop : CaretBottom" /></el-icon>
                {{ Math.abs(metric.change) }}% vs 上一周期
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-col>
      
      <el-col :span="16">
        <el-card shadow="hover" class="mb-5" v-loading="loading.priceTrend">
          <template #header>
            <div class="card-header">
              <span>竞品价格趋势分析</span>
            </div>
          </template>
          <v-chart class="chart" :option="priceTrendOption" autoresize />
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card shadow="hover" class="mb-5" v-loading="loading.promotionAnalysis">
          <template #header>
            <div class="card-header">
              <span>促销策略倾向分析</span>
            </div>
          </template>
          <v-chart class="chart" :option="promotionAnalysisOption" autoresize />
        </el-card>
      </el-col>

      <el-col :span="24">
        <el-card shadow="hover" class="mb-5" v-loading="loading.categoryDistribution">
          <template #header>
            <div class="card-header">
              <span>类目价格带分布</span>
            </div>
          </template>
          <v-chart class="chart" :option="categoryDistributionOption" autoresize />
        </el-card>
      </el-col>

      <el-col :span="24">
        <el-card shadow="hover" v-loading="loading.report">
           <template #header>
            <div class="card-header flex justify-between items-center">
              <span>综合分析报告</span>
               <el-button type="success" size="small" @click="generateReport" :loading="loading.report">
                 <el-icon><Document /></el-icon>
                 重新生成报告
               </el-button>
            </div>
          </template>
          <div class="report-content prose" v-html="comprehensiveReport"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, provide } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { LineChart, PieChart, BarChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  ToolboxComponent,
  DataZoomComponent,
} from 'echarts/components';
import VChart, { THEME_KEY } from 'vue-echarts';
import { ElMessage } from 'element-plus';
import { Search, Refresh, CaretTop, CaretBottom, Document } from '@element-plus/icons-vue';
import api from '../api/analytics'; // 假设的API模块

// ECharts 按需引入
use([
  CanvasRenderer,
  LineChart,
  PieChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  ToolboxComponent,
  DataZoomComponent,
]);

provide(THEME_KEY, 'light');

const loading = reactive({
  main: false,
  priceTrend: false,
  promotionAnalysis: false,
  categoryDistribution: false,
  report: false,
});

const filters = reactive({
  dateRange: [
    new Date(new Date().setDate(new Date().getDate() - 30)).toISOString().split('T')[0],
    new Date().toISOString().split('T')[0]
  ],
  categoryId: '',
  keyword: '',
});

const categoryOptions = ref([]); //
const keyMetrics = ref([
  { title: '总体促销强度指数', value: 'N/A', change: 0 },
  { title: '平均价格偏差率', value: 'N/A', change: 0 },
  { title: '最低价SKU占比', value: 'N/A', change: 0 },
  { title: '监控产品总数', value: 'N/A', change: 0 },
]);

const priceTrendOption = ref({});
const promotionAnalysisOption = ref({});
const categoryDistributionOption = ref({});
const comprehensiveReport = ref('<p>请点击生成报告按钮以获取最新分析。</p>');

const dateShortcuts = [
  { text: '最近一周', value: () => { const end = new Date(); const start = new Date(); start.setTime(start.getTime() - 3600 * 1000 * 24 * 7); return [start, end]; } },
  { text: '最近一个月', value: () => { const end = new Date(); const start = new Date(); start.setTime(start.getTime() - 3600 * 1000 * 24 * 30); return [start, end]; } },
  { text: '最近三个月', value: () => { const end = new Date(); const start = new Date(); start.setTime(start.getTime() - 3600 * 1000 * 24 * 90); return [start, end]; } },
];

const getApiFilters = () => ({
  start_date: filters.dateRange ? filters.dateRange[0] : null,
  end_date: filters.dateRange ? filters.dateRange[1] : null,
  category_id: filters.categoryId,
  keyword: filters.keyword,
});

const fetchCategories = async () => {
  try {
    // 假设有获取类目的API
    // const response = await api.getCategories();
    // categoryOptions.value = response.data;
    categoryOptions.value = [
      { id: 1, name: '手机数码' },
      { id: 2, name: '家用电器' },
      { id: 3, name: '电脑办公' },
    ];
  } catch (error) {
    ElMessage.error('获取产品类目失败');
  }
};

const fetchData = async () => {
  loading.main = true;
  await Promise.all([
    fetchKeyMetrics(),
    fetchPriceTrend(),
    fetchPromotionAnalysis(),
    fetchCategoryDistribution(),
  ]);
  loading.main = false;
};

const fetchKeyMetrics = async () => {
  try {
    const apiFilters = getApiFilters();
    const [promoIntensity, productMetrics] = await Promise.all([
       api.getPromotionIntensityIndex(apiFilters),
       api.calculateProductMetrics(apiFilters)
    ]);

    keyMetrics.value = [
        { title: '总体促销强度指数', value: promoIntensity.data?.intensity_index?.toFixed(2) || 'N/A', change: promoIntensity.data?.change_rate?.toFixed(2) || 0 },
        { title: '平均价格偏差率', value: `${productMetrics.data?.average_price_deviation_rate?.toFixed(2) || 'N/A'}%`, change: 0 }, // 变化率需要额外逻辑
        { title: '最低价SKU占比', value: `${productMetrics.data?.lowest_price_sku_ratio?.toFixed(2) || 'N/A'}%`, change: 0 },
        { title: '监控产品总数', value: productMetrics.data?.total_products || 'N/A', change: 0 },
    ];

  } catch (error) {
     ElMessage.error('获取核心指标失败');
  }
};

const fetchPriceTrend = async () => {
  loading.priceTrend = true;
  try {
    const response = await api.getPriceTrendChart(getApiFilters());
    priceTrendOption.value = {
      tooltip: { trigger: 'axis' },
      legend: { data: response.data.legend || [] },
      grid: { left: '3%', right: '4%', bottom: '10%', containLabel: true },
      toolbox: { feature: { saveAsImage: {} } },
      xAxis: { type: 'category', boundaryGap: false, data: response.data.dates || [] },
      yAxis: { type: 'value', axisLabel: { formatter: '￥{value}' } },
      dataZoom: [{ type: 'inside' }, { type: 'slider' }],
      series: response.data.series || [],
    };
  } catch (error) {
    ElMessage.error('获取价格趋势数据失败');
  } finally {
    loading.priceTrend = false;
  }
};

const fetchPromotionAnalysis = async () => {
  loading.promotionAnalysis = true;
  try {
    const response = await api.getPromotionAnalysis(getApiFilters());
    promotionAnalysisOption.value = {
      tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: {c} ({d}%)' },
      legend: { top: 'bottom' },
      series: [
        {
          name: '促销类型',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: { show: false, position: 'center' },
          emphasis: {
            label: { show: true, fontSize: '20', fontWeight: 'bold' }
          },
          labelLine: { show: false },
          data: response.data || [],
        },
      ],
    };
  } catch (error) {
    ElMessage.error('获取促销分析数据失败');
  } finally {
    loading.promotionAnalysis = false;
  }
};

const fetchCategoryDistribution = async () => {
  loading.categoryDistribution = true;
  try {
    const response = await api.getCategoryPriceDistribution(getApiFilters());
    categoryDistributionOption.value = {
      tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
      grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
      xAxis: { type: 'category', data: response.data.map(item => item.price_range) },
      yAxis: { type: 'value', name: '产品数量' },
      series: [
        {
          name: '产品数',
          type: 'bar',
          data: response.data.map(item => item.count),
          barWidth: '60%',
        },
      ],
    };
  } catch (error) {
    ElMessage.error('获取价格带分布数据失败');
  } finally {
    loading.categoryDistribution = false;
  }
};

const generateReport = async () => {
  loading.report = true;
  try {
    const response = await api.getComprehensiveAnalysisReport(getApiFilters());
    // 简单的Markdown转HTML
    comprehensiveReport.value = response.data.report
      .replace(/## (.*)/g, '<h3>$1</h3>')
      .replace(/### (.*)/g, '<h4>$1</h4>')
      .replace(/\* \*(.*)\* \*/g, '<strong>$1</strong>')
      .replace(/\* (.*)/g, '<li>$1</li>')
      .replace(/(\r\n|\n|\r)/g, '<br/>');
  } catch (error) {
    ElMessage.error('生成综合报告失败');
  } finally {
    loading.report = false;
  }
};


const resetFilters = () => {
  filters.dateRange = [
    new Date(new Date().setDate(new Date().getDate() - 30)).toISOString().split('T')[0],
    new Date().toISOString().split('T')[0]
  ];
  filters.categoryId = '';
  filters.keyword = '';
  fetchData();
};

onMounted(() => {
  fetchCategories();
  fetchData();
});
</script>

<style scoped>
.competitor-dashboard {
  height: 100%;
}
.chart {
  height: 350px;
}
.report-content {
  min-height: 200px;
  line-height: 1.8;
}

/* 使用 Tailwind a-like prose class for simple styling */
.prose h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
}
.prose h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.25rem;
}
.prose li {
  margin-left: 1.5rem;
  list-style-type: disc;
}
</style> 