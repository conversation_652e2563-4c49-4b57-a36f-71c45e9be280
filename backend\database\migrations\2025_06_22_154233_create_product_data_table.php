<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_data', function (Blueprint $table) {
            $table->id();
            $table->foreignId('data_source_id')->constrained('data_sources')->onDelete('cascade')->comment('数据源ID');
            $table->string('item_id', 100)->comment('外部商品/数据唯一ID');
            $table->json('raw_data')->comment('从API获取的原始数据');
            $table->json('standardized_data')->comment('根据映射规则标准化后的数据');
            $table->timestamp('last_collected_at')->comment('最后采集时间');
            $table->timestamps();

            // 添加索引以提高查询性能
            $table->unique(['data_source_id', 'item_id']);
            $table->index('last_collected_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_data');
    }
};
