<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\TaskGroup;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;

class TaskGroupController extends Controller
{
    /**
     * 获取任务组列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = TaskGroup::with(['user', 'monitoringTasks'])
            ->where('user_id', Auth::id());
        
        // 支持搜索
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }
        
        // 按状态筛选
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }
        
        // 排序
        $sortBy = $request->get('sort_by', 'sort_order');
        $sortOrder = $request->get('sort_order', 'asc');
        
        // 如果按sort_order排序，添加created_at作为次要排序
        if ($sortBy === 'sort_order') {
            $query->orderBy('sort_order', $sortOrder)
                  ->orderBy('created_at', 'desc');
        } else {
            $query->orderBy($sortBy, $sortOrder);
        }
        
        // 分页
        $perPage = $request->get('per_page', 15);
        $taskGroups = $query->paginate($perPage);
        
        return response()->json([
            'success' => true,
            'data' => $taskGroups,
            'message' => '任务组列表获取成功'
        ]);
    }
    
    /**
     * 获取所有任务组（用于下拉选择）
     */
    public function all(): JsonResponse
    {
        $taskGroups = TaskGroup::where('user_id', Auth::id())
            ->where('status', 1)
            ->orderBy('sort_order', 'asc')
            ->orderBy('created_at', 'desc')
            ->get(['id', 'name', 'description']);
        
        return response()->json([
            'success' => true,
            'data' => $taskGroups,
            'message' => '任务组列表获取成功'
        ]);
    }
    
    /**
     * 获取单个任务组详情
     */
    public function show(TaskGroup $taskGroup): JsonResponse
    {
        // 确保用户只能查看自己的任务组
        if ($taskGroup->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权访问该任务组'
            ], 403);
        }
        
        $taskGroup->load(['user', 'monitoringTasks' => function ($query) {
            $query->with(['dataSource']);
        }]);
        
        return response()->json([
            'success' => true,
            'data' => $taskGroup,
            'message' => '任务组详情获取成功'
        ]);
    }
    
    /**
     * 创建新任务组
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:100',
            'description' => 'nullable|string|max:1000',
            'sort_order' => 'nullable|integer|min:0',
            'status' => 'nullable|integer|in:0,1',
        ]);
        
        // 如果没有指定排序，设置为最大值+1
        if (!isset($validated['sort_order'])) {
            $maxSortOrder = TaskGroup::where('user_id', Auth::id())->max('sort_order') ?? 0;
            $validated['sort_order'] = $maxSortOrder + 1;
        }
        
        $taskGroup = TaskGroup::create([
            'name' => $validated['name'],
            'description' => $validated['description'] ?? '',
            'user_id' => Auth::id(),
            'sort_order' => $validated['sort_order'],
            'status' => $validated['status'] ?? 1,
        ]);
        
        $taskGroup->load(['user']);
        
        return response()->json([
            'success' => true,
            'data' => $taskGroup,
            'message' => '任务组创建成功'
        ], 201);
    }
    
    /**
     * 更新任务组
     */
    public function update(Request $request, TaskGroup $taskGroup): JsonResponse
    {
        // 确保用户只能更新自己的任务组
        if ($taskGroup->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权修改该任务组'
            ], 403);
        }
        
        $validated = $request->validate([
            'name' => 'required|string|max:100',
            'description' => 'nullable|string|max:1000',
            'sort_order' => 'nullable|integer|min:0',
            'status' => 'nullable|integer|in:0,1',
        ]);
        
        $taskGroup->update([
            'name' => $validated['name'],
            'description' => $validated['description'] ?? $taskGroup->description,
            'sort_order' => $validated['sort_order'] ?? $taskGroup->sort_order,
            'status' => $validated['status'] ?? $taskGroup->status,
        ]);
        
        $taskGroup->load(['user']);
        
        return response()->json([
            'success' => true,
            'data' => $taskGroup,
            'message' => '任务组更新成功'
        ]);
    }
    
    /**
     * 删除任务组
     */
    public function destroy(TaskGroup $taskGroup): JsonResponse
    {
        // 确保用户只能删除自己的任务组
        if ($taskGroup->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权删除该任务组'
            ], 403);
        }
        
        // 检查任务组下是否有监控任务
        $taskCount = $taskGroup->monitoringTasks()->count();
        if ($taskCount > 0) {
            return response()->json([
                'success' => false,
                'message' => "该任务组下还有 {$taskCount} 个监控任务，请先移除或删除这些任务"
            ], 422);
        }
        
        $taskGroup->delete();
        
        return response()->json([
            'success' => true,
            'message' => '任务组删除成功'
        ]);
    }
    
    /**
     * 批量删除任务组
     */
    public function batchDestroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => 'required|array|min:1',
            'ids.*' => 'integer|exists:task_groups,id',
        ]);
        
        $taskGroups = TaskGroup::whereIn('id', $validated['ids'])
            ->where('user_id', Auth::id())
            ->with('monitoringTasks')
            ->get();
        
        if ($taskGroups->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => '没有找到可删除的任务组'
            ], 404);
        }
        
        // 检查是否有任务组下还有监控任务
        $groupsWithTasks = $taskGroups->filter(function ($group) {
            return $group->monitoringTasks->count() > 0;
        });
        
        if ($groupsWithTasks->isNotEmpty()) {
            $groupNames = $groupsWithTasks->pluck('name')->join('、');
            return response()->json([
                'success' => false,
                'message' => "以下任务组下还有监控任务，请先移除或删除这些任务：{$groupNames}"
            ], 422);
        }
        
        $deletedCount = $taskGroups->count();
        TaskGroup::whereIn('id', $taskGroups->pluck('id'))->delete();
        
        return response()->json([
            'success' => true,
            'message' => "成功删除 {$deletedCount} 个任务组"
        ]);
    }
    
    /**
     * 更新任务组状态
     */
    public function updateStatus(Request $request, TaskGroup $taskGroup): JsonResponse
    {
        // 确保用户只能更新自己的任务组
        if ($taskGroup->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权修改该任务组状态'
            ], 403);
        }
        
        $validated = $request->validate([
            'status' => 'required|integer|in:0,1',
        ]);
        
        $taskGroup->update([
            'status' => $validated['status']
        ]);
        
        return response()->json([
            'success' => true,
            'data' => $taskGroup,
            'message' => '任务组状态更新成功'
        ]);
    }
    
    /**
     * 批量更新排序
     */
    public function updateSortOrder(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'sort_data' => 'required|array|min:1',
            'sort_data.*.id' => 'required|integer|exists:task_groups,id',
            'sort_data.*.sort_order' => 'required|integer|min:0',
        ]);
        
        $taskGroupIds = collect($validated['sort_data'])->pluck('id');
        
        // 确保所有任务组都属于当前用户
        $taskGroups = TaskGroup::whereIn('id', $taskGroupIds)
            ->where('user_id', Auth::id())
            ->get();
        
        if ($taskGroups->count() !== $taskGroupIds->count()) {
            return response()->json([
                'success' => false,
                'message' => '包含不属于您的任务组'
            ], 403);
        }
        
        // 批量更新排序
        foreach ($validated['sort_data'] as $sortItem) {
            TaskGroup::where('id', $sortItem['id'])
                ->where('user_id', Auth::id())
                ->update(['sort_order' => $sortItem['sort_order']]);
        }
        
        return response()->json([
            'success' => true,
            'message' => '排序更新成功'
        ]);
    }
    
    /**
     * 获取任务组统计信息
     */
    public function statistics(TaskGroup $taskGroup): JsonResponse
    {
        // 确保用户只能查看自己的任务组
        if ($taskGroup->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => '无权查看该任务组统计'
            ], 403);
        }
        
        $tasks = $taskGroup->monitoringTasks();
        
        $statistics = [
            'basic_info' => [
                'id' => $taskGroup->id,
                'name' => $taskGroup->name,
                'status' => $taskGroup->status,
                'created_at' => $taskGroup->created_at,
            ],
            'task_stats' => [
                'total_tasks' => $tasks->count(),
                'running_tasks' => $tasks->where('status', 'running')->count(),
                'pending_tasks' => $tasks->where('status', 'pending')->count(),
                'paused_tasks' => $tasks->where('status', 'paused')->count(),
                'stopped_tasks' => $tasks->where('status', 'stopped')->count(),
                'failed_tasks' => $tasks->where('status', 'failed')->count(),
            ],
            'execution_stats' => [
                'total_runs' => $tasks->sum('run_count'),
                'total_successes' => $tasks->sum('success_count'),
                'total_failures' => $tasks->sum('failed_count'),
                'success_rate' => $tasks->count() > 0 ? 
                    round(($tasks->sum('success_count') / max($tasks->sum('run_count'), 1)) * 100, 2) : 0,
            ],
            'recent_tasks' => $tasks->with(['dataSource'])
                ->latest()
                ->limit(5)
                ->get(['id', 'name', 'status', 'data_source_id', 'last_run_at']),
        ];
        
        return response()->json([
            'success' => true,
            'data' => $statistics,
            'message' => '任务组统计信息获取成功'
        ]);
    }
} 