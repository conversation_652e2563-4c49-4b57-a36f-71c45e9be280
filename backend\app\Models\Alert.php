<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Alert extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'monitoring_task_id',
        'alert_rule_id',
        'product_id',
        'alert_type',
        'alert_level',
        'title',
        'message',
        'trigger_data',
        'status',
        'handled_at',
        'handled_by',
        'handle_note',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'trigger_data' => 'array',
        'status' => 'integer',
        'handled_at' => 'datetime',
    ];

    /**
     * 所属监控任务
     */
    public function monitoringTask(): BelongsTo
    {
        return $this->belongsTo(MonitoringTask::class);
    }

    /**
     * 预警规则
     */
    public function alertRule(): BelongsTo
    {
        return $this->belongsTo(AlertRule::class);
    }

    /**
     * 处理人
     */
    public function handler(): BelongsTo
    {
        return $this->belongsTo(User::class, 'handled_by');
    }
} 