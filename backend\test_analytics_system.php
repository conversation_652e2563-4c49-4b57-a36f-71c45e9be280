<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use App\Services\AnalyticsService;
use App\Models\ProductData;
use App\Models\DataSource;
use Carbon\Carbon;

// 初始化Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== 电商市场动态监测系统 - 分析系统测试 ===\n\n";

try {
    $analyticsService = new AnalyticsService();
    
    // 1. 创建测试数据源
    echo "1. 创建测试数据源...\n";
    $dataSource = DataSource::firstOrCreate([
        'name' => '淘宝API测试',
        'type' => 'api',
        'config' => [
            'base_url' => 'http://60.247.148.208:5001/tb/new/item_detail_base',
            'token' => 'testcc85S9zszzs'
        ],
        'status' => 'active'
    ]);
    echo "数据源创建成功，ID: {$dataSource->id}\n\n";
    
    // 2. 创建测试产品数据
    echo "2. 创建测试产品数据...\n";
    $testProducts = [
        [
            'item_id' => '************',
            'standardized_data' => [
                'Code' => '200',
                'Id' => '************',
                'Title' => '米其林轮胎 PRIMACY 4 235/50R18 101Y',
                'Price' => 899.00,
                'subPrice' => 799.00,
                'subPriceTitle' => '券后价',
                'Quantity' => 100,
                'Sale' => 1500,
                'CommentCount' => 3200,
                'State' => 1,
                'is_Sku' => 1,
                'itemType' => '天猫',
                'category_Id' => '50012131',
                'category_Path' => '汽车零部件->轮胎->乘用车轮胎',
                'shopId' => '548713772',
                'shopName' => '米其林官方旗舰店',
                'Promotion' => [
                    [
                        'content' => '满减',
                        'sub_content' => '满300减50',
                        'type' => 'discount'
                    ],
                    [
                        'content' => '折扣',
                        'sub_content' => '限时8.9折',
                        'type' => 'discount'
                    ]
                ],
                '最低到手价' => 799.00,
                '最高到手价' => 899.00
            ]
        ],
        [
            'item_id' => '************',
            'standardized_data' => [
                'Code' => '200',
                'Id' => '************',
                'Title' => '普利司通轮胎 TURANZA T005 235/50R18 101Y',
                'Price' => 849.00,
                'subPrice' => 729.00,
                'subPriceTitle' => '券后价',
                'Quantity' => 80,
                'Sale' => 1200,
                'CommentCount' => 2800,
                'State' => 1,
                'is_Sku' => 1,
                'itemType' => '天猫',
                'category_Id' => '50012131',
                'category_Path' => '汽车零部件->轮胎->乘用车轮胎',
                'shopId' => '548713773',
                'shopName' => '普利司通官方旗舰店',
                'Promotion' => [
                    [
                        'content' => '折扣',
                        'sub_content' => '全场8.6折',
                        'type' => 'discount'
                    ]
                ],
                '最低到手价' => 729.00,
                '最高到手价' => 849.00
            ]
        ],
        [
            'item_id' => '************',
            'standardized_data' => [
                'Code' => '200',
                'Id' => '************',
                'Title' => '马牌轮胎 ContiPremiumContact 6 235/50R18 101Y',
                'Price' => 789.00,
                'subPrice' => 789.00,
                'subPriceTitle' => '原价',
                'Quantity' => 60,
                'Sale' => 800,
                'CommentCount' => 1500,
                'State' => 1,
                'is_Sku' => 1,
                'itemType' => '天猫',
                'category_Id' => '50012131',
                'category_Path' => '汽车零部件->轮胎->乘用车轮胎',
                'shopId' => '548713774',
                'shopName' => '马牌官方旗舰店',
                'Promotion' => [],
                '最低到手价' => 789.00,
                '最高到手价' => 789.00
            ]
        ]
    ];
    
    foreach ($testProducts as $productData) {
        ProductData::updateOrCreate(
            [
                'data_source_id' => $dataSource->id,
                'item_id' => $productData['item_id']
            ],
            [
                'raw_data' => $productData['standardized_data'],
                'standardized_data' => $productData['standardized_data'],
                'last_collected_at' => Carbon::now()
            ]
        );
    }
    echo "测试产品数据创建成功\n\n";
    
    // 3. 测试促销策略分析
    echo "3. 测试促销策略分析...\n";
    $productDataCollection = ProductData::where('data_source_id', $dataSource->id)->get();
    $promotionAnalysis = $analyticsService->analyzePromotionStrategyTrend($productDataCollection);
    
    echo "促销类型分析:\n";
    foreach ($promotionAnalysis['promotion_types'] as $type => $data) {
        echo "  - {$type}: {$data['count']}次 ({$data['percentage']}%)\n";
    }
    
    echo "\n促销策略分析:\n";
    foreach ($promotionAnalysis['promotion_strategies'] as $strategy => $data) {
        echo "  - {$strategy}: {$data['count']}次 ({$data['percentage']}%)\n";
    }
    echo "总促销数量: {$promotionAnalysis['total_promotions']}\n\n";
    
    // 4. 测试单品价格指标计算
    echo "4. 测试单品价格指标计算...\n";
    $productData = $testProducts[0]['standardized_data'];
    
    // 促销价偏离率
    $promotionDeviationRate = $analyticsService->calculatePromotionPriceDeviationRate($productData);
    echo "促销价偏离率: " . round($promotionDeviationRate, 2) . "%\n";
    
    // 综合折扣率
    $comprehensiveDiscountRate = $analyticsService->calculateProductComprehensiveDiscountRate($productData);
    echo "综合折扣率: " . round($comprehensiveDiscountRate, 2) . "%\n";
    
    // 渠道价格偏离率（假设官方指导价为900元）
    $channelDeviationRate = $analyticsService->calculateChannelPriceDeviationRate($productData, 900);
    echo "渠道价格偏离率（官方指导价900元）: " . round($channelDeviationRate, 2) . "%\n";
    
    // 价格偏差率（假设我方指导价为850元）
    $priceDeviationRate = $analyticsService->calculateProductPriceDeviationRate($productData, 850);
    echo "价格偏差率（我方指导价850元）: " . round($priceDeviationRate, 2) . "%\n\n";
    
    // 5. 测试整体促销强度指数
    echo "5. 测试整体促销强度指数...\n";
    $promotionIntensityIndex = $analyticsService->calculateOverallPromotionIntensityIndex($productDataCollection);
    echo "整体促销强度指数: " . round($promotionIntensityIndex, 2) . "\n\n";
    
    // 6. 测试价格趋势分析（创建历史数据）
    echo "6. 测试价格趋势分析...\n";
    
    // 为第一个产品创建历史价格数据
    $itemId = '************';
    $basePrices = [820, 815, 810, 805, 799, 795, 790, 799, 805, 810];
    
    for ($i = 0; $i < 10; $i++) {
        $historicalData = $testProducts[0]['standardized_data'];
        $historicalData['subPrice'] = $basePrices[$i];
        $historicalData['Price'] = $basePrices[$i] + 100;
        
        ProductData::create([
            'data_source_id' => $dataSource->id,
            'item_id' => $itemId . '_history_' . $i,
            'raw_data' => $historicalData,
            'standardized_data' => $historicalData,
            'last_collected_at' => Carbon::now()->subDays(10 - $i)
        ]);
    }
    
    // 使用历史数据项目进行趋势分析
    $trendAnalysis = $analyticsService->calculatePriceTrendSlope($itemId . '_history_0', 15);
    echo "价格趋势分析结果:\n";
    echo "  - 斜率: {$trendAnalysis['slope']}\n";
    echo "  - 趋势: {$trendAnalysis['trend']}\n";
    echo "  - 数据点数: {$trendAnalysis['data_points']}\n";
    echo "  - R²: {$trendAnalysis['r_squared']}\n\n";
    
    // 7. 测试类目价格分布
    echo "7. 测试类目价格分布...\n";
    $categoryDistribution = $analyticsService->getCategoryPriceDistribution();
    
    echo "类目价格分布:\n";
    foreach ($categoryDistribution as $category => $priceRanges) {
        echo "  类目: {$category}\n";
        foreach ($priceRanges as $range => $count) {
            echo "    - {$range}元: {$count}个商品\n";
        }
    }
    echo "\n";
    
    // 8. 测试综合分析报告
    echo "8. 测试综合分析报告...\n";
    $itemIds = ['************', '************', '************'];
    $options = [
        'our_guide_prices' => [
            '************' => 850,
            '************' => 800,
            '************' => 750
        ],
        'include_trend_analysis' => false
    ];
    
    $comprehensiveReport = $analyticsService->generateComprehensiveAnalysisReport($itemIds, $options);
    
    echo "综合分析报告:\n";
    echo "  - 总产品数: {$comprehensiveReport['summary']['total_products']}\n";
    echo "  - 分析产品数: {$comprehensiveReport['summary']['analyzed_products']}\n";
    echo "  - 整体促销强度: " . round($comprehensiveReport['overall_promotion_intensity'], 2) . "\n";
    
    echo "\n价格分析详情:\n";
    foreach ($comprehensiveReport['price_analysis'] as $itemId => $analysis) {
        echo "  商品: {$analysis['product_name']}\n";
        echo "    - 当前价格: {$analysis['current_price']}元\n";
        echo "    - 当前到手价: {$analysis['current_sub_price']}元\n";
        echo "    - 促销偏离率: " . round($analysis['promotion_deviation_rate'], 2) . "%\n";
        echo "    - 综合折扣率: " . round($analysis['comprehensive_discount_rate'], 2) . "%\n";
        if (isset($analysis['price_deviation_rate'])) {
            echo "    - 价格偏差率: " . round($analysis['price_deviation_rate'], 2) . "%\n";
        }
        echo "\n";
    }
    
    // 9. 测试缓存清理
    echo "9. 测试缓存清理...\n";
    $cacheCleared = $analyticsService->clearAnalysisCache();
    echo "缓存清理结果: " . ($cacheCleared ? '成功' : '失败') . "\n\n";
    
    echo "=== 分析系统测试完成 ===\n";
    echo "所有功能测试通过！分析系统已准备就绪。\n\n";
    
    echo "API接口测试建议:\n";
    echo "1. GET /api/analytics/promotion-analysis - 获取促销策略分析\n";
    echo "2. GET /api/analytics/price-trend/{item_id} - 获取价格趋势分析\n";
    echo "3. POST /api/analytics/product-metrics - 计算产品指标\n";
    echo "4. GET /api/analytics/promotion-intensity - 获取促销强度指数\n";
    echo "5. POST /api/analytics/comprehensive-report - 生成综合报告\n";
    
} catch (Exception $e) {
    echo "测试失败: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "错误堆栈:\n" . $e->getTraceAsString() . "\n";
} 