<template>
  <div class="similar-dashboard">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon><Histogram /></el-icon>
            相似同款数据看板
          </h1>
          <p class="page-description">查看监控任务的历史搜索结果和数据分析</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
          <el-button @click="exportData">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon total">
              <el-icon><DataBoard /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.totalProducts }}</div>
              <div class="stat-label">监控商品总数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon found">
              <el-icon><Search /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.foundSimilar }}</div>
              <div class="stat-label">发现同款数量</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon new">
              <el-icon><Plus /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.newToday }}</div>
              <div class="stat-label">今日新增</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon risk">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.riskItems }}</div>
              <div class="stat-label">风险商品</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>筛选条件</span>
            <el-button type="text" @click="resetFilters">重置</el-button>
          </div>
        </template>
        <el-form :model="filters" inline>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="filters.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item label="商品标题">
            <el-input
              v-model="filters.title"
              placeholder="请输入商品标题关键词"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="类目">
            <el-select v-model="filters.category" placeholder="请选择类目" clearable>
              <el-option label="服装鞋帽" value="clothing" />
              <el-option label="数码家电" value="electronics" />
              <el-option label="家居用品" value="home" />
              <el-option label="美妆护肤" value="beauty" />
              <el-option label="食品饮料" value="food" />
            </el-select>
          </el-form-item>
          <el-form-item label="相似度">
            <el-slider
              v-model="filters.similarity"
              :min="0"
              :max="100"
              show-input
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="applyFilters">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>同款商品列表</span>
            <div class="header-actions">
              <el-button-group>
                <el-button :type="viewMode === 'table' ? 'primary' : ''" @click="viewMode = 'table'">
                  <el-icon><List /></el-icon>
                  列表视图
                </el-button>
                <el-button :type="viewMode === 'grid' ? 'primary' : ''" @click="viewMode = 'grid'">
                  <el-icon><Grid /></el-icon>
                  网格视图
                </el-button>
              </el-button-group>
            </div>
          </div>
        </template>

        <!-- 表格视图 -->
        <div v-if="viewMode === 'table'">
          <el-table
            :data="tableData"
            v-loading="loading"
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column label="商品信息" width="300">
              <template #default="scope">
                <div class="product-info">
                  <el-image
                    :src="scope.row.image"
                    class="product-image"
                    fit="cover"
                    :preview-src-list="[scope.row.image]"
                  />
                  <div class="product-details">
                    <div class="product-title">{{ scope.row.title }}</div>
                    <div class="product-id">ID: {{ scope.row.id }}</div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="shop" label="店铺" width="150" />
            <el-table-column prop="platform" label="平台" width="100">
              <template #default="scope">
                <el-tag :type="getPlatformType(scope.row.platform)">
                  {{ scope.row.platform }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="price" label="价格" width="120">
              <template #default="scope">
                <span class="price">¥{{ scope.row.price }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="similarity" label="相似度" width="120">
              <template #default="scope">
                <el-progress
                  :percentage="scope.row.similarity"
                  :color="getSimilarityColor(scope.row.similarity)"
                  :stroke-width="8"
                />
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="foundDate" label="发现时间" width="150" />
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button type="primary" size="small" @click="viewDetails(scope.row)">
                  查看详情
                </el-button>
                <el-button type="warning" size="small" @click="markAsRisk(scope.row)">
                  标记风险
                </el-button>
                <el-dropdown @command="handleAction">
                  <el-button size="small">
                    更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="{action: 'compare', row: scope.row}">
                        对比分析
                      </el-dropdown-item>
                      <el-dropdown-item :command="{action: 'track', row: scope.row}">
                        加入跟踪
                      </el-dropdown-item>
                      <el-dropdown-item :command="{action: 'report', row: scope.row}">
                        举报侵权
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 网格视图 -->
        <div v-if="viewMode === 'grid'" class="grid-view">
          <el-row :gutter="20">
            <el-col :span="6" v-for="item in tableData" :key="item.id">
              <div class="product-card" @click="viewDetails(item)">
                <div class="card-image">
                  <el-image :src="item.image" fit="cover" />
                  <div class="similarity-badge">
                    {{ item.similarity }}%
                  </div>
                </div>
                <div class="card-content">
                  <h4 class="card-title">{{ item.title }}</h4>
                  <div class="card-info">
                    <span class="shop">{{ item.shop }}</span>
                    <span class="price">¥{{ item.price }}</span>
                  </div>
                  <div class="card-meta">
                    <el-tag size="small" :type="getPlatformType(item.platform)">
                      {{ item.platform }}
                    </el-tag>
                    <span class="found-date">{{ item.foundDate }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialog.visible"
      title="商品详情"
      width="800px"
      :before-close="handleCloseDetail"
    >
      <div v-if="detailDialog.data" class="detail-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-image">
              <el-image :src="detailDialog.data.image" fit="contain" />
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-info">
              <h3>{{ detailDialog.data.title }}</h3>
              <el-descriptions :column="1" border>
                <el-descriptions-item label="商品ID">
                  {{ detailDialog.data.id }}
                </el-descriptions-item>
                <el-descriptions-item label="店铺">
                  {{ detailDialog.data.shop }}
                </el-descriptions-item>
                <el-descriptions-item label="平台">
                  <el-tag :type="getPlatformType(detailDialog.data.platform)">
                    {{ detailDialog.data.platform }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="价格">
                  <span class="price">¥{{ detailDialog.data.price }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="相似度">
                  <el-progress
                    :percentage="detailDialog.data.similarity"
                    :color="getSimilarityColor(detailDialog.data.similarity)"
                  />
                </el-descriptions-item>
                <el-descriptions-item label="状态">
                  <el-tag :type="getStatusType(detailDialog.data.status)">
                    {{ getStatusText(detailDialog.data.status) }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="发现时间">
                  {{ detailDialog.data.foundDate }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-col>
        </el-row>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialog.visible = false">关闭</el-button>
          <el-button type="primary" @click="handleDetailAction">
            查看原商品
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Histogram,
  Refresh,
  Download,
  DataBoard,
  Search,
  Plus,
  Warning,
  List,
  Grid,
  ArrowDown
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const viewMode = ref('table')

// 统计数据
const stats = reactive({
  totalProducts: 1248,
  foundSimilar: 326,
  newToday: 23,
  riskItems: 15
})

// 筛选条件
const filters = reactive({
  dateRange: [],
  title: '',
  category: '',
  similarity: 80
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 326
})

// 表格数据
const tableData = ref([
  {
    id: 'P001',
    title: '夏季新款连衣裙女装韩版修身显瘦中长款',
    image: 'https://picsum.photos/200/200?random=1',
    shop: '时尚女装店',
    platform: '淘宝',
    price: 159.00,
    similarity: 95,
    status: 'normal',
    foundDate: '2024-01-15 10:30:00'
  },
  {
    id: 'P002',
    title: '韩版修身连衣裙夏季新款女装中长款',
    image: 'https://picsum.photos/200/200?random=2',
    shop: '潮流服饰',
    platform: '京东',
    price: 139.00,
    similarity: 88,
    status: 'risk',
    foundDate: '2024-01-15 11:20:00'
  },
  {
    id: 'P003',
    title: '夏装新品连衣裙女韩版显瘦中长款裙子',
    image: 'https://picsum.photos/200/200?random=3',
    shop: '优品女装',
    platform: '拼多多',
    price: 89.00,
    similarity: 82,
    status: 'tracking',
    foundDate: '2024-01-15 14:15:00'
  }
])

// 选中的行
const selectedRows = ref([])

// 详情对话框
const detailDialog = reactive({
  visible: false,
  data: null
})

// 方法
const refreshData = () => {
  loading.value = true
  // TODO: 调用API刷新数据
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据刷新成功')
  }, 1000)
}

const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

const resetFilters = () => {
  filters.dateRange = []
  filters.title = ''
  filters.category = ''
  filters.similarity = 80
  applyFilters()
}

const applyFilters = () => {
  loading.value = true
  // TODO: 根据筛选条件查询数据
  setTimeout(() => {
    loading.value = false
    ElMessage.success('筛选完成')
  }, 500)
}

const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  // TODO: 重新加载数据
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  // TODO: 重新加载数据
}

const viewDetails = (row: any) => {
  detailDialog.data = row
  detailDialog.visible = true
}

const markAsRisk = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要将商品"${row.title}"标记为风险商品吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // TODO: 调用API标记风险
    row.status = 'risk'
    ElMessage.success('已标记为风险商品')
  } catch (error) {
    // 用户取消操作
  }
}

const handleAction = (command: any) => {
  const { action, row } = command
  
  switch (action) {
    case 'compare':
      ElMessage.info('对比分析功能开发中...')
      break
    case 'track':
      ElMessage.info('加入跟踪功能开发中...')
      break
    case 'report':
      ElMessage.info('举报侵权功能开发中...')
      break
  }
}

const handleCloseDetail = () => {
  detailDialog.visible = false
  detailDialog.data = null
}

const handleDetailAction = () => {
  ElMessage.info('查看原商品功能开发中...')
}

// 工具方法
const getPlatformType = (platform: string) => {
  const types: Record<string, string> = {
    '淘宝': 'warning',
    '京东': 'danger',
    '拼多多': 'success',
    '天猫': 'info'
  }
  return types[platform] || ''
}

const getSimilarityColor = (similarity: number) => {
  if (similarity >= 90) return '#67c23a'
  if (similarity >= 80) return '#e6a23c'
  return '#f56c6c'
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    'normal': 'success',
    'risk': 'danger',
    'tracking': 'warning'
  }
  return types[status] || ''
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    'normal': '正常',
    'risk': '风险',
    'tracking': '跟踪中'
  }
  return texts[status] || status
}

// 生命周期
onMounted(() => {
  // TODO: 加载初始数据
})
</script>

<style scoped>
.similar-dashboard {
  padding: 0;
}

/* 页面头部 */
.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  color: #7f8c8d;
  margin: 0;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.stat-icon.total { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stat-icon.found { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stat-icon.new { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stat-icon.risk { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
}

.stat-label {
  color: #7f8c8d;
  font-size: 14px;
  margin-top: 4px;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

/* 表格区域 */
.table-section {
  margin-bottom: 20px;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-image {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  flex-shrink: 0;
}

.product-details {
  flex: 1;
  min-width: 0;
}

.product-title {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-id {
  font-size: 12px;
  color: #95a5a6;
}

.price {
  font-weight: 600;
  color: #e74c3c;
}

/* 网格视图 */
.grid-view {
  margin-bottom: 20px;
}

.product-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  margin-bottom: 20px;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.card-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.card-image .el-image {
  width: 100%;
  height: 100%;
}

.similarity-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.card-content {
  padding: 16px;
}

.card-title {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin: 0 0 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.shop {
  font-size: 12px;
  color: #7f8c8d;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.found-date {
  font-size: 12px;
  color: #95a5a6;
}

/* 分页 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

/* 详情对话框 */
.detail-content {
  padding: 20px 0;
}

.detail-image {
  text-align: center;
}

.detail-image .el-image {
  max-width: 100%;
  max-height: 400px;
  border-radius: 8px;
}

.detail-info h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .stats-cards .el-col {
    margin-bottom: 16px;
  }
  
  .filter-section .el-form {
    flex-direction: column;
  }
  
  .filter-section .el-form-item {
    margin-right: 0;
    margin-bottom: 16px;
  }
}
</style> 