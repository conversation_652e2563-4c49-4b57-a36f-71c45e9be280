<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('task_groups', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('分组名称');
            $table->text('description')->nullable()->comment('分组描述');
            $table->string('color', 7)->default('#1890ff')->comment('分组颜色');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->tinyInteger('status')->default(1)->comment('状态：1-启用，0-禁用');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade')->comment('创建者ID');
            $table->integer('task_count')->default(0)->comment('任务数量');
            $table->timestamps();
            
            $table->index(['user_id', 'status']);
            $table->index('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('task_groups');
    }
};
