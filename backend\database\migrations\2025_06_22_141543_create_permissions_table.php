<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('permissions', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->unique()->comment('权限名称');
            $table->string('display_name', 100)->comment('显示名称');
            $table->text('description')->nullable()->comment('权限描述');
            $table->string('group', 50)->default('default')->comment('权限分组');
            $table->string('resource', 100)->nullable()->comment('资源标识');
            $table->string('action', 50)->nullable()->comment('操作类型');
            $table->tinyInteger('status')->default(1)->comment('状态：1-启用，0-禁用');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->timestamps();
            
            $table->index(['group', 'status']);
            $table->index(['resource', 'action']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('permissions');
    }
};
