<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\Http;

/**
 * 预警规则API测试脚本
 * 
 * 使用方法：
 * php test_alert_rules.php
 */

class AlertRuleApiTester
{
    private $baseUrl;
    private $token;
    private $userId;
    private $createdRuleIds = [];

    public function __construct()
    {
        $this->baseUrl = 'http://localhost:8000/api';
        $this->output("=== 预警规则API测试开始 ===\n");
    }

    /**
     * 运行所有测试
     */
    public function runTests()
    {
        try {
            // 1. 用户登录获取token
            $this->login();
            
            // 2. 获取可用选项
            $this->testGetOptions();
            
            // 3. 创建预警规则
            $this->testCreateAlertRule();
            
            // 4. 获取预警规则列表
            $this->testGetAlertRules();
            
            // 5. 获取单个预警规则详情
            $this->testGetAlertRule();
            
            // 6. 更新预警规则
            $this->testUpdateAlertRule();
            
            // 7. 测试规则状态管理
            $this->testRuleStatusManagement();
            
            // 8. 获取统计信息
            $this->testGetStatistics();
            
            // 9. 测试批量操作
            $this->testBatchOperations();
            
            // 10. 测试搜索和筛选
            $this->testSearchAndFilter();
            
            // 11. 测试验证规则
            $this->testValidation();
            
            // 12. 清理测试数据
            $this->cleanup();
            
            $this->output("\n=== 所有测试完成 ===");
            
        } catch (Exception $e) {
            $this->output("测试失败: " . $e->getMessage());
            $this->cleanup();
        }
    }

    /**
     * 用户登录
     */
    private function login()
    {
        $this->output("1. 测试用户登录...");
        
        $response = $this->makeRequest('POST', '/auth/login', [
            'username' => 'admin',
            'password' => 'password123'
        ]);

        if ($response['success']) {
            $this->token = $response['data']['token'];
            $this->userId = $response['data']['user']['id'];
            $this->output("   ✓ 登录成功，获取到token");
        } else {
            throw new Exception("登录失败: " . ($response['message'] ?? '未知错误'));
        }
    }

    /**
     * 测试获取可用选项
     */
    private function testGetOptions()
    {
        $this->output("2. 测试获取可用选项...");
        
        $response = $this->makeRequest('GET', '/alert-rules/options');
        
        if ($response['success']) {
            $options = $response['data'];
            $this->output("   ✓ 规则类型: " . implode(', ', $options['rule_types']));
            $this->output("   ✓ 操作符: " . implode(', ', $options['operators']));
            $this->output("   ✓ 严重级别: " . implode(', ', $options['severity_levels']));
            $this->output("   ✓ 通知渠道: " . implode(', ', $options['notification_channels']));
        } else {
            throw new Exception("获取选项失败: " . ($response['message'] ?? '未知错误'));
        }
    }

    /**
     * 测试创建预警规则
     */
    private function testCreateAlertRule()
    {
        $this->output("3. 测试创建预警规则...");
        
        // 创建多个不同类型的预警规则
        $rules = [
            [
                'name' => '价格异常预警',
                'description' => '监控商品价格异常变化',
                'rule_type' => 'threshold',
                'target_field' => 'price',
                'operator' => 'greater_than',
                'threshold' => '1000',
                'severity' => 'high',
                'notification_channels' => ['email', 'database'],
                'recipients' => ['<EMAIL>'],
                'cooldown_minutes' => 30,
                'max_alerts_per_hour' => 5
            ],
            [
                'name' => '库存不足预警',
                'description' => '监控商品库存数量',
                'rule_type' => 'threshold',
                'target_field' => 'stock',
                'operator' => 'less_than',
                'threshold' => '10',
                'severity' => 'medium',
                'notification_channels' => ['email'],
                'recipients' => ['<EMAIL>']
            ],
            [
                'name' => '数据变化预警',
                'description' => '监控数据变化率',
                'rule_type' => 'change_rate',
                'target_field' => 'total_count',
                'operator' => 'change_percent_greater_than',
                'threshold' => '20',
                'severity' => 'low',
                'notification_channels' => ['database']
            ]
        ];

        foreach ($rules as $index => $ruleData) {
            $response = $this->makeRequest('POST', '/alert-rules', $ruleData);
            
            if ($response['success']) {
                $rule = $response['data'];
                $this->createdRuleIds[] = $rule['id'];
                $this->output("   ✓ 创建预警规则 #{$rule['id']}: {$rule['name']}");
            } else {
                throw new Exception("创建预警规则失败: " . ($response['message'] ?? '未知错误'));
            }
        }
    }

    /**
     * 测试获取预警规则列表
     */
    private function testGetAlertRules()
    {
        $this->output("4. 测试获取预警规则列表...");
        
        $response = $this->makeRequest('GET', '/alert-rules');
        
        if ($response['success']) {
            $rules = $response['data']['data'];
            $this->output("   ✓ 获取到 " . count($rules) . " 个预警规则");
            
            // 测试分页
            $response = $this->makeRequest('GET', '/alert-rules?per_page=2&page=1');
            if ($response['success']) {
                $this->output("   ✓ 分页功能正常");
            }
        } else {
            throw new Exception("获取预警规则列表失败: " . ($response['message'] ?? '未知错误'));
        }
    }

    /**
     * 测试获取单个预警规则详情
     */
    private function testGetAlertRule()
    {
        $this->output("5. 测试获取单个预警规则详情...");
        
        if (empty($this->createdRuleIds)) {
            throw new Exception("没有可用的预警规则ID");
        }

        $ruleId = $this->createdRuleIds[0];
        $response = $this->makeRequest('GET', "/alert-rules/{$ruleId}");
        
        if ($response['success']) {
            $rule = $response['data'];
            $this->output("   ✓ 获取预警规则详情: {$rule['name']}");
            $this->output("   ✓ 规则类型: {$rule['rule_type']}");
            $this->output("   ✓ 目标字段: {$rule['target_field']}");
            $this->output("   ✓ 操作符: {$rule['operator']}");
            $this->output("   ✓ 阈值: {$rule['threshold']}");
        } else {
            throw new Exception("获取预警规则详情失败: " . ($response['message'] ?? '未知错误'));
        }
    }

    /**
     * 测试更新预警规则
     */
    private function testUpdateAlertRule()
    {
        $this->output("6. 测试更新预警规则...");
        
        if (empty($this->createdRuleIds)) {
            throw new Exception("没有可用的预警规则ID");
        }

        $ruleId = $this->createdRuleIds[0];
        $updateData = [
            'name' => '价格异常预警(已更新)',
            'description' => '更新后的描述',
            'threshold' => '1500',
            'severity' => 'critical',
            'cooldown_minutes' => 60
        ];

        $response = $this->makeRequest('PUT', "/alert-rules/{$ruleId}", $updateData);
        
        if ($response['success']) {
            $rule = $response['data'];
            $this->output("   ✓ 更新预警规则成功: {$rule['name']}");
            $this->output("   ✓ 新阈值: {$rule['threshold']}");
            $this->output("   ✓ 新严重级别: {$rule['severity']}");
        } else {
            throw new Exception("更新预警规则失败: " . ($response['message'] ?? '未知错误'));
        }
    }

    /**
     * 测试规则状态管理
     */
    private function testRuleStatusManagement()
    {
        $this->output("7. 测试规则状态管理...");
        
        if (empty($this->createdRuleIds)) {
            throw new Exception("没有可用的预警规则ID");
        }

        $ruleId = $this->createdRuleIds[0];

        // 测试禁用规则
        $response = $this->makeRequest('PATCH', "/alert-rules/{$ruleId}/deactivate");
        if ($response['success']) {
            $this->output("   ✓ 禁用预警规则成功");
        } else {
            throw new Exception("禁用预警规则失败: " . ($response['message'] ?? '未知错误'));
        }

        // 测试启用规则
        $response = $this->makeRequest('PATCH', "/alert-rules/{$ruleId}/activate");
        if ($response['success']) {
            $this->output("   ✓ 启用预警规则成功");
        } else {
            throw new Exception("启用预警规则失败: " . ($response['message'] ?? '未知错误'));
        }
    }

    /**
     * 测试获取统计信息
     */
    private function testGetStatistics()
    {
        $this->output("8. 测试获取统计信息...");
        
        $response = $this->makeRequest('GET', '/alert-rules/statistics');
        
        if ($response['success']) {
            $stats = $response['data'];
            $this->output("   ✓ 总规则数: {$stats['total_rules']}");
            $this->output("   ✓ 活跃规则数: {$stats['active_rules']}");
            $this->output("   ✓ 非活跃规则数: {$stats['inactive_rules']}");
            
            if (!empty($stats['rules_by_type'])) {
                $this->output("   ✓ 按类型统计: " . json_encode($stats['rules_by_type']));
            }
            
            if (!empty($stats['rules_by_severity'])) {
                $this->output("   ✓ 按严重级别统计: " . json_encode($stats['rules_by_severity']));
            }
        } else {
            throw new Exception("获取统计信息失败: " . ($response['message'] ?? '未知错误'));
        }
    }

    /**
     * 测试批量操作
     */
    private function testBatchOperations()
    {
        $this->output("9. 测试批量操作...");
        
        if (count($this->createdRuleIds) < 2) {
            $this->output("   ! 跳过批量操作测试（规则数量不足）");
            return;
        }

        // 测试批量更新状态
        $ruleIds = array_slice($this->createdRuleIds, 0, 2);
        $response = $this->makeRequest('PATCH', '/alert-rules/batch-status', [
            'ids' => $ruleIds,
            'status' => 'inactive'
        ]);
        
        if ($response['success']) {
            $this->output("   ✓ 批量禁用规则成功，影响 {$response['data']['updated_count']} 个规则");
        } else {
            throw new Exception("批量更新状态失败: " . ($response['message'] ?? '未知错误'));
        }

        // 恢复状态
        $response = $this->makeRequest('PATCH', '/alert-rules/batch-status', [
            'ids' => $ruleIds,
            'status' => 'active'
        ]);
        
        if ($response['success']) {
            $this->output("   ✓ 批量启用规则成功");
        }
    }

    /**
     * 测试搜索和筛选
     */
    private function testSearchAndFilter()
    {
        $this->output("10. 测试搜索和筛选...");
        
        // 测试按名称搜索
        $response = $this->makeRequest('GET', '/alert-rules?search=价格');
        if ($response['success']) {
            $this->output("   ✓ 按名称搜索功能正常");
        }

        // 测试按状态筛选
        $response = $this->makeRequest('GET', '/alert-rules?status=active');
        if ($response['success']) {
            $this->output("   ✓ 按状态筛选功能正常");
        }

        // 测试按规则类型筛选
        $response = $this->makeRequest('GET', '/alert-rules?rule_type=threshold');
        if ($response['success']) {
            $this->output("   ✓ 按规则类型筛选功能正常");
        }

        // 测试按严重级别筛选
        $response = $this->makeRequest('GET', '/alert-rules?severity=high');
        if ($response['success']) {
            $this->output("   ✓ 按严重级别筛选功能正常");
        }

        // 测试排序
        $response = $this->makeRequest('GET', '/alert-rules?sort_by=name&sort_order=asc');
        if ($response['success']) {
            $this->output("   ✓ 排序功能正常");
        }
    }

    /**
     * 测试验证规则
     */
    private function testValidation()
    {
        $this->output("11. 测试验证规则...");
        
        // 测试必填字段验证
        $response = $this->makeRequest('POST', '/alert-rules', [
            'description' => '缺少必填字段的测试'
        ]);
        
        if (!$response['success']) {
            $this->output("   ✓ 必填字段验证正常");
        } else {
            throw new Exception("必填字段验证失败，应该返回错误");
        }

        // 测试无效规则类型
        $response = $this->makeRequest('POST', '/alert-rules', [
            'name' => '无效规则类型测试',
            'rule_type' => 'invalid_type',
            'target_field' => 'test_field',
            'operator' => 'equals',
            'threshold' => '100',
            'severity' => 'low'
        ]);
        
        if (!$response['success']) {
            $this->output("   ✓ 规则类型验证正常");
        } else {
            throw new Exception("规则类型验证失败，应该返回错误");
        }

        // 测试无效操作符
        $response = $this->makeRequest('POST', '/alert-rules', [
            'name' => '无效操作符测试',
            'rule_type' => 'threshold',
            'target_field' => 'test_field',
            'operator' => 'invalid_operator',
            'threshold' => '100',
            'severity' => 'low'
        ]);
        
        if (!$response['success']) {
            $this->output("   ✓ 操作符验证正常");
        } else {
            throw new Exception("操作符验证失败，应该返回错误");
        }

        // 测试无效严重级别
        $response = $this->makeRequest('POST', '/alert-rules', [
            'name' => '无效严重级别测试',
            'rule_type' => 'threshold',
            'target_field' => 'test_field',
            'operator' => 'equals',
            'threshold' => '100',
            'severity' => 'invalid_severity'
        ]);
        
        if (!$response['success']) {
            $this->output("   ✓ 严重级别验证正常");
        } else {
            throw new Exception("严重级别验证失败，应该返回错误");
        }

        // 测试无效邮箱格式
        $response = $this->makeRequest('POST', '/alert-rules', [
            'name' => '无效邮箱格式测试',
            'rule_type' => 'threshold',
            'target_field' => 'test_field',
            'operator' => 'equals',
            'threshold' => '100',
            'severity' => 'low',
            'recipients' => ['invalid-email']
        ]);
        
        if (!$response['success']) {
            $this->output("   ✓ 邮箱格式验证正常");
        } else {
            throw new Exception("邮箱格式验证失败，应该返回错误");
        }
    }

    /**
     * 清理测试数据
     */
    private function cleanup()
    {
        $this->output("12. 清理测试数据...");
        
        if (!empty($this->createdRuleIds)) {
            // 批量删除创建的规则
            $response = $this->makeRequest('DELETE', '/alert-rules', [
                'ids' => $this->createdRuleIds
            ]);
            
            if ($response['success']) {
                $this->output("   ✓ 清理完成，删除了 {$response['data']['deleted_count']} 个预警规则");
            } else {
                $this->output("   ! 清理失败: " . ($response['message'] ?? '未知错误'));
            }
        }
    }

    /**
     * 发送HTTP请求
     */
    private function makeRequest($method, $endpoint, $data = [])
    {
        $url = $this->baseUrl . $endpoint;
        $headers = ['Accept' => 'application/json'];
        
        if ($this->token) {
            $headers['Authorization'] = 'Bearer ' . $this->token;
        }

        $options = [
            'http' => [
                'method' => $method,
                'header' => $this->buildHeaders($headers),
                'content' => $method !== 'GET' ? json_encode($data) : null
            ]
        ];

        if ($method !== 'GET' && !empty($data)) {
            $headers['Content-Type'] = 'application/json';
            $options['http']['header'] = $this->buildHeaders($headers);
        }

        $context = stream_context_create($options);
        $result = @file_get_contents($url, false, $context);
        
        if ($result === false) {
            throw new Exception("请求失败: {$method} {$url}");
        }

        $response = json_decode($result, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("响应解析失败: " . json_last_error_msg());
        }

        return $response;
    }

    /**
     * 构建请求头
     */
    private function buildHeaders($headers)
    {
        $headerStrings = [];
        foreach ($headers as $key => $value) {
            $headerStrings[] = "{$key}: {$value}";
        }
        return implode("\r\n", $headerStrings);
    }

    /**
     * 输出信息
     */
    private function output($message)
    {
        echo $message . "\n";
    }
}

// 运行测试
try {
    $tester = new AlertRuleApiTester();
    $tester->runTests();
} catch (Exception $e) {
    echo "测试异常: " . $e->getMessage() . "\n";
    exit(1);
} 