<template>
  <div class="audit-logs">
    <div class="page-header">
      <h1>操作日志</h1>
      <p>查看系统用户操作记录和审计信息</p>
    </div>

    <!-- 筛选工具栏 -->
    <el-card class="filter-card">
      <el-form :model="filters" :inline="true" class="filter-form">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filters.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item label="用户">
          <el-select v-model="filters.userId" placeholder="选择用户" clearable style="width: 150px">
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.name"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="操作类型">
          <el-select v-model="filters.action" placeholder="选择操作类型" clearable style="width: 150px">
            <el-option
              v-for="action in actionTypes"
              :key="action.value"
              :label="action.label"
              :value="action.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="模块">
          <el-select v-model="filters.module" placeholder="选择模块" clearable style="width: 150px">
            <el-option
              v-for="module in modules"
              :key="module.value"
              :label="module.label"
              :value="module.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="结果">
          <el-select v-model="filters.status" placeholder="选择结果" clearable style="width: 120px">
            <el-option label="成功" value="success" />
            <el-option label="失败" value="failed" />
          </el-select>
        </el-form-item>
        <el-form-item label="关键词">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索日志内容"
            style="width: 200px"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">
            搜索
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">
            重置
          </el-button>
          <el-button :icon="Download" @click="handleExport">
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 日志列表 -->
    <el-card class="table-card">
      <el-table
        :data="logs"
        v-loading="loading"
        stripe
        class="audit-table"
        @row-click="showLogDetail"
      >
        <el-table-column prop="created_at" label="时间" width="180" sortable>
          <template #default="{ row }">
            {{ new Date(row.created_at).toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="user_name" label="用户" width="120" />
        <el-table-column prop="action" label="操作" width="100">
          <template #default="{ row }">
            <el-tag :type="getActionTagType(row.action)" size="small">
              {{ getActionLabel(row.action) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="module" label="模块" width="120">
          <template #default="{ row }">
            <el-tag type="info" size="small">
              {{ getModuleLabel(row.module) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="操作描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="ip_address" label="IP地址" width="140" />
        <el-table-column prop="user_agent" label="浏览器" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            {{ getUserAgentInfo(row.user_agent) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="结果" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 'success' ? 'success' : 'danger'" size="small">
              {{ row.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="primary" @click.stop="showLogDetail(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="totalLogs"
          :page-sizes="[20, 50, 100, 200]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 日志详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="操作日志详情" width="800px">
      <div class="log-detail" v-if="selectedLog">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="操作时间">
            {{ new Date(selectedLog.created_at).toLocaleString() }}
          </el-descriptions-item>
          <el-descriptions-item label="操作用户">
            {{ selectedLog.user_name }}
          </el-descriptions-item>
          <el-descriptions-item label="操作类型">
            <el-tag :type="getActionTagType(selectedLog.action)" size="small">
              {{ getActionLabel(selectedLog.action) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="所属模块">
            <el-tag type="info" size="small">
              {{ getModuleLabel(selectedLog.module) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="操作结果">
            <el-tag :type="selectedLog.status === 'success' ? 'success' : 'danger'" size="small">
              {{ selectedLog.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="IP地址">
            {{ selectedLog.ip_address }}
          </el-descriptions-item>
          <el-descriptions-item label="操作描述" :span="2">
            {{ selectedLog.description }}
          </el-descriptions-item>
          <el-descriptions-item label="用户代理" :span="2">
            {{ selectedLog.user_agent }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 请求详情 -->
        <div class="request-details" v-if="selectedLog.request_data">
          <h4>请求数据</h4>
          <el-input
            :model-value="JSON.stringify(selectedLog.request_data, null, 2)"
            type="textarea"
            :rows="6"
            readonly
          />
        </div>

        <!-- 响应详情 -->
        <div class="response-details" v-if="selectedLog.response_data">
          <h4>响应数据</h4>
          <el-input
            :model-value="JSON.stringify(selectedLog.response_data, null, 2)"
            type="textarea"
            :rows="6"
            readonly
          />
        </div>

        <!-- 错误信息 -->
        <div class="error-details" v-if="selectedLog.error_message">
          <h4>错误信息</h4>
          <el-alert
            :title="selectedLog.error_message"
            type="error"
            :closable="false"
            show-icon
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Download } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const totalLogs = ref(0)
const showDetailDialog = ref(false)
const selectedLog = ref(null)

// 筛选条件
const filters = ref({
  dateRange: [],
  userId: null,
  action: null,
  module: null,
  status: null,
  keyword: ''
})

// 用户列表
const users = ref([
  { id: 1, name: '系统管理员' },
  { id: 2, name: '普通用户' }
])

// 操作类型
const actionTypes = ref([
  { value: 'create', label: '创建' },
  { value: 'update', label: '更新' },
  { value: 'delete', label: '删除' },
  { value: 'login', label: '登录' },
  { value: 'logout', label: '退出' },
  { value: 'view', label: '查看' },
  { value: 'export', label: '导出' },
  { value: 'import', label: '导入' }
])

// 模块列表
const modules = ref([
  { value: 'user', label: '用户管理' },
  { value: 'role', label: '角色管理' },
  { value: 'datasource', label: '数据源管理' },
  { value: 'monitoring', label: '监控任务' },
  { value: 'alert', label: '预警管理' },
  { value: 'system', label: '系统设置' }
])

// 模拟日志数据
const logs = ref([
  {
    id: 1,
    user_name: '系统管理员',
    action: 'login',
    module: 'user',
    description: '用户登录系统',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    status: 'success',
    created_at: '2024-01-15T10:30:00Z',
    request_data: { username: 'admin' },
    response_data: { token: 'xxx', user_id: 1 }
  },
  {
    id: 2,
    user_name: '系统管理员',
    action: 'create',
    module: 'datasource',
    description: '创建数据源：淘宝商品详情接口',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    status: 'success',
    created_at: '2024-01-15T11:00:00Z',
    request_data: { name: '淘宝商品详情接口', url: 'http://api.example.com' },
    response_data: { id: 1, status: 'created' }
  },
  {
    id: 3,
    user_name: '普通用户',
    action: 'create',
    module: 'monitoring',
    description: '创建监控任务：品牌A价格监控',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    status: 'success',
    created_at: '2024-01-15T14:20:00Z',
    request_data: { name: '品牌A价格监控', products: ['123', '456'] }
  },
  {
    id: 4,
    user_name: '普通用户',
    action: 'delete',
    module: 'monitoring',
    description: '删除监控任务失败：权限不足',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    status: 'failed',
    created_at: '2024-01-15T15:10:00Z',
    error_message: '用户权限不足，无法删除该监控任务'
  }
])

// 方法
const getActionTagType = (action) => {
  const typeMap = {
    create: 'success',
    update: 'warning',
    delete: 'danger',
    login: 'primary',
    logout: 'info',
    view: 'info',
    export: 'warning',
    import: 'warning'
  }
  return typeMap[action] || 'info'
}

const getActionLabel = (action) => {
  const actionType = actionTypes.value.find(type => type.value === action)
  return actionType ? actionType.label : action
}

const getModuleLabel = (module) => {
  const moduleInfo = modules.value.find(mod => mod.value === module)
  return moduleInfo ? moduleInfo.label : module
}

const getUserAgentInfo = (userAgent) => {
  if (!userAgent) return ''
  
  // 简单的浏览器识别
  if (userAgent.includes('Chrome')) return 'Chrome'
  if (userAgent.includes('Firefox')) return 'Firefox'
  if (userAgent.includes('Safari')) return 'Safari'
  if (userAgent.includes('Edge')) return 'Edge'
  
  return '其他'
}

const handleSearch = () => {
  currentPage.value = 1
  loadLogs()
}

const handleReset = () => {
  filters.value = {
    dateRange: [],
    userId: null,
    action: null,
    module: null,
    status: null,
    keyword: ''
  }
  currentPage.value = 1
  loadLogs()
}

const handleExport = async () => {
  try {
    // TODO: 调用API导出日志
    ElMessage.success('日志导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const handleSizeChange = (size) => {
  pageSize.value = size
  loadLogs()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadLogs()
}

const showLogDetail = (row) => {
  selectedLog.value = row
  showDetailDialog.value = true
}

const loadLogs = async () => {
  loading.value = true
  try {
    // TODO: 调用API加载日志数据
    // 这里使用模拟数据
    totalLogs.value = logs.value.length
    
    await new Promise(resolve => setTimeout(resolve, 500))
  } catch (error) {
    ElMessage.error('加载日志失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadLogs()
})
</script>

<style scoped lang="scss">
.audit-logs {
  .page-header {
    margin-bottom: 24px;
    
    h1 {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #303133;
    }
    
    p {
      color: #606266;
      margin: 0;
    }
  }
  
  .filter-card {
    margin-bottom: 16px;
    
    .filter-form {
      :deep(.el-form-item) {
        margin-bottom: 12px;
      }
    }
  }
  
  .table-card {
    .audit-table {
      :deep(.el-table__row) {
        cursor: pointer;
        
        &:hover {
          background-color: #f5f7fa;
        }
      }
    }
    
    .pagination-wrapper {
      display: flex;
      justify-content: flex-end;
      margin-top: 16px;
    }
  }
  
  .log-detail {
    .request-details,
    .response-details,
    .error-details {
      margin-top: 20px;
      
      h4 {
        margin-bottom: 10px;
        color: #303133;
        font-size: 14px;
        font-weight: 600;
      }
    }
  }
}
</style> 