<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use App\Models\DataSource;
use App\Models\TaskGroup;

class StoreMonitoringTaskRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // 任何登录的用户都可以尝试创建任务，具体的权限在规则中验证
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:100',
            'description' => 'nullable|string|max:1000',
            'task_group_id' => 'nullable|exists:task_groups,id',
            'data_source_id' => 'required|exists:data_sources,id',
            'target_products' => 'nullable|array',
            'monitor_fields' => 'nullable|array',
            'frequency_type' => 'required|in:interval,cron',
            'frequency_value' => 'required_if:frequency_type,interval|integer|min:1',
            'cron_expression' => 'required_if:frequency_type,cron|string|max:100',
            'auto_start' => 'boolean',
            'notify_on_error' => 'boolean',
            'notification_config' => 'nullable|array',
        ];
    }
    
    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // 验证数据源是否属于当前用户或者是公共的
            $dataSource = DataSource::find($this->input('data_source_id'));
            if (!$dataSource || ($dataSource->owner_id !== Auth::id() && $dataSource->status !== 1)) {
                 $validator->errors()->add('data_source_id', '无效的数据源或无权访问。');
            }
            
            // 如果指定了任务组，验证任务组是否属于当前用户
            if ($this->filled('task_group_id')) {
                $taskGroup = TaskGroup::find($this->input('task_group_id'));
                if (!$taskGroup || $taskGroup->user_id !== Auth::id()) {
                    $validator->errors()->add('task_group_id', '无效的任务组或无权访问。');
                }
            }
        });
    }
} 