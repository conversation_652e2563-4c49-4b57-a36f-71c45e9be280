<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\AnalyticsService;
use App\Models\ProductData;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class AnalyticsController extends Controller
{
    protected AnalyticsService $analyticsService;

    public function __construct(AnalyticsService $analyticsService)
    {
        $this->analyticsService = $analyticsService;
    }

    /**
     * 获取仪表盘统计数据
     */
    public function getDashboardStatistics(): JsonResponse
    {
        try {
            $statistics = $this->analyticsService->getDashboardStatistics();
            return response()->json([
                'success' => true,
                'data' => $statistics,
                'message' => '仪表盘统计数据获取成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取仪表盘统计数据失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取促销策略分析
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getPromotionAnalysis(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'item_ids' => 'array',
                'item_ids.*' => 'string',
                'data_source_id' => 'integer|exists:data_sources,id',
                'days' => 'integer|min:1|max:365'
            ]);

            $query = ProductData::query();
            
            if (isset($validated['item_ids'])) {
                $query->whereIn('item_id', $validated['item_ids']);
            }
            
            if (isset($validated['data_source_id'])) {
                $query->whereHas('monitoringTask', function ($q) use ($validated) {
                    $q->where('data_source_id', $validated['data_source_id']);
                });
            }

            if (isset($validated['days'])) {
                $query->where('last_collected_at', '>=', now()->subDays($validated['days']));
            }

            $productDataCollection = $query->get();
            $analysis = $this->analyticsService->analyzePromotionStrategyTrend($productDataCollection);

            return response()->json([
                'success' => true,
                'data' => $analysis
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取促销策略分析失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取价格趋势分析
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getPriceTrendAnalysis(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'item_id' => 'required|string',
                'days' => 'integer|min:1|max:365'
            ]);

            $itemId = $validated['item_id'];
            $days = $validated['days'] ?? 30;

            $trendAnalysis = $this->analyticsService->calculatePriceTrendSlope($itemId, $days);

            return response()->json([
                'success' => true,
                'data' => $trendAnalysis
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取价格趋势分析失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取价格趋势图表数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getPriceTrendChart(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'item_ids' => 'array',
                'item_ids.*' => 'string',
                'days' => 'integer|min:1|max:365'
            ]);

            // 如果没有指定商品，获取前10个商品作为示例
            if (!isset($validated['item_ids']) || empty($validated['item_ids'])) {
                $sampleProducts = ProductData::select('item_id')
                    ->distinct()
                    ->take(5)
                    ->pluck('item_id')
                    ->toArray();
                
                if (empty($sampleProducts)) {
                    return response()->json([
                        'success' => true,
                        'data' => [
                            'dates' => [],
                            'series' => [],
                            'legend' => [],
                            'message' => '暂无产品数据'
                        ]
                    ]);
                }
                
                $itemIds = $sampleProducts;
            } else {
                $itemIds = $validated['item_ids'];
            }

            $days = $validated['days'] ?? 30;

            $chartData = $this->analyticsService->getPriceTrendChartData($itemIds, $days);

            return response()->json([
                'success' => true,
                'data' => $chartData
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取价格趋势图表数据失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 计算单个产品的价格指标
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function calculateProductMetrics(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'item_ids' => 'array',
                'item_ids.*' => 'string',
                'item_id' => 'string',
                'our_guide_price' => 'numeric|min:0',
                'our_min_price' => 'numeric|min:0',
                'our_max_price' => 'numeric|min:0',
                'official_price' => 'numeric|min:0'
            ]);

            // 如果没有指定商品，获取所有商品的统计数据
            if (!isset($validated['item_id']) && !isset($validated['item_ids'])) {
                $query = ProductData::query();
                $productDataCollection = $query->take(100)->get();
                
                if ($productDataCollection->isEmpty()) {
                    return response()->json([
                        'success' => true,
                        'data' => [
                            'total_products' => 0,
                            'average_price_deviation_rate' => 0,
                            'lowest_price_sku_ratio' => 0,
                            'message' => '暂无产品数据'
                        ]
                    ]);
                }
                
                // 计算统计指标
                $totalProducts = $productDataCollection->count();
                $averagePriceDeviation = 0; // 这里可以根据实际业务逻辑计算
                $lowestPriceRatio = 0; // 这里可以根据实际业务逻辑计算
                
                return response()->json([
                    'success' => true,
                    'data' => [
                        'total_products' => $totalProducts,
                        'average_price_deviation_rate' => $averagePriceDeviation,
                        'lowest_price_sku_ratio' => $lowestPriceRatio,
                        'analyzed_at' => now()->toDateTimeString()
                    ]
                ]);
            }

            // 处理单个商品
            if (isset($validated['item_id'])) {
                $productData = ProductData::where('item_id', $validated['item_id'])
                    ->orderBy('last_collected_at', 'desc')
                    ->first();

                if (!$productData) {
                    return response()->json([
                        'success' => false,
                        'message' => '未找到该商品的数据'
                    ], 404);
                }
            } else {
                // 处理多个商品，取第一个作为示例
                $itemIds = $validated['item_ids'];
                $productData = ProductData::whereIn('item_id', $itemIds)
                    ->orderBy('last_collected_at', 'desc')
                    ->first();

                if (!$productData) {
                    return response()->json([
                        'success' => false,
                        'message' => '未找到指定商品的数据'
                    ], 404);
                }
            }

            $standardizedData = $productData->standardized_data;
            $metrics = [];

            // 促销价偏离率
            $metrics['promotion_price_deviation_rate'] = $this->analyticsService
                ->calculatePromotionPriceDeviationRate($standardizedData);

            // 综合折扣率
            $metrics['comprehensive_discount_rate'] = $this->analyticsService
                ->calculateProductComprehensiveDiscountRate($standardizedData);

            // 如果提供了官方指导价，计算渠道价格偏离率
            if (isset($validated['official_price'])) {
                $metrics['channel_price_deviation_rate'] = $this->analyticsService
                    ->calculateChannelPriceDeviationRate($standardizedData, $validated['official_price']);
            }

            // 如果提供了我方指导价，计算价格偏差率
            if (isset($validated['our_guide_price'])) {
                $metrics['price_deviation_rate'] = $this->analyticsService
                    ->calculateProductPriceDeviationRate($standardizedData, $validated['our_guide_price']);
            }

            // 如果提供了我方最低价，计算最低价偏差率
            if (isset($validated['our_min_price'])) {
                $metrics['min_price_deviation_rate'] = $this->analyticsService
                    ->calculateProductMinPriceDeviationRate($standardizedData, $validated['our_min_price']);
            }

            // 如果提供了我方最高价，计算最高价偏差率
            if (isset($validated['our_max_price'])) {
                $metrics['max_price_deviation_rate'] = $this->analyticsService
                    ->calculateProductMaxPriceDeviationRate($standardizedData, $validated['our_max_price']);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'item_id' => $validated['item_id'] ?? $productData->item_id,
                    'product_name' => $standardizedData['Title'] ?? '未知商品',
                    'current_price' => $standardizedData['Price'] ?? 0,
                    'current_sub_price' => $standardizedData['subPrice'] ?? 0,
                    'metrics' => $metrics,
                    'last_updated' => $productData->last_collected_at
                ]
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '计算产品指标失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取整体促销强度指数
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getPromotionIntensityIndex(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'item_ids' => 'array',
                'item_ids.*' => 'string',
                'data_source_id' => 'integer|exists:data_sources,id',
                'days' => 'integer|min:1|max:365'
            ]);

            $query = ProductData::query();
            
            if (isset($validated['item_ids'])) {
                $query->whereIn('item_id', $validated['item_ids']);
            }
            
            if (isset($validated['data_source_id'])) {
                $query->whereHas('monitoringTask', function ($q) use ($validated) {
                    $q->where('data_source_id', $validated['data_source_id']);
                });
            }

            if (isset($validated['days'])) {
                $query->where('last_collected_at', '>=', now()->subDays($validated['days']));
            }

            $productDataCollection = $query->get();
            $intensityIndex = $this->analyticsService->calculateOverallPromotionIntensityIndex($productDataCollection);

            return response()->json([
                'success' => true,
                'data' => [
                    'promotion_intensity_index' => $intensityIndex,
                    'analyzed_products' => $productDataCollection->count(),
                    'analysis_date' => now()->toDateTimeString()
                ]
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取促销强度指数失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取类目价格分布
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getCategoryPriceDistribution(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'price_ranges' => 'array',
                'price_ranges.*' => 'array|size:2',
                'price_ranges.*.*' => 'numeric|min:0'
            ]);

            $priceRanges = $validated['price_ranges'] ?? null;
            $distribution = $this->analyticsService->getCategoryPriceDistribution($priceRanges);

            return response()->json([
                'success' => true,
                'data' => $distribution
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取类目价格分布失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 生成综合分析报告
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getComprehensiveAnalysisReport(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'item_ids' => 'array',
                'item_ids.*' => 'string',
                'our_guide_prices' => 'array',
                'our_guide_prices.*' => 'numeric|min:0',
                'include_trend_analysis' => 'boolean',
                'trend_days' => 'integer|min:1|max:365'
            ]);

            // 如果没有指定商品，获取示例商品
            if (!isset($validated['item_ids']) || empty($validated['item_ids'])) {
                $sampleProducts = ProductData::select('item_id')
                    ->distinct()
                    ->take(10)
                    ->pluck('item_id')
                    ->toArray();
                
                if (empty($sampleProducts)) {
                    return response()->json([
                        'success' => true,
                        'data' => [
                            'report_html' => '<p>暂无产品数据，无法生成分析报告。</p>',
                            'summary' => '暂无数据',
                            'generated_at' => now()->toDateTimeString()
                        ]
                    ]);
                }
                
                $itemIds = $sampleProducts;
            } else {
                $itemIds = $validated['item_ids'];
            }
            
            $options = array_intersect_key($validated, [
                'our_guide_prices' => '',
                'include_trend_analysis' => '',
                'trend_days' => ''
            ]);

            $report = $this->analyticsService->generateComprehensiveAnalysisReport($itemIds, $options);

            return response()->json([
                'success' => true,
                'data' => $report
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '生成综合分析报告失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 清除分析缓存
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function clearCache(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'pattern' => 'string|nullable'
            ]);

            $pattern = $validated['pattern'] ?? null;
            $result = $this->analyticsService->clearAnalysisCache($pattern);

            return response()->json([
                'success' => $result,
                'message' => $result ? '缓存清除成功' : '缓存清除失败'
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '清除缓存失败: ' . $e->getMessage()
            ], 500);
        }
    }
} 