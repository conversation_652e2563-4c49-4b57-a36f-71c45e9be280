<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 检查管理员用户 ===\n";

$user = App\Models\User::where('username', 'admin')->first();

if ($user) {
    echo "管理员用户找到:\n";
    echo "ID: " . $user->id . "\n";
    echo "用户名: " . $user->username . "\n";
    echo "邮箱: " . $user->email . "\n";
    echo "状态: " . $user->status . "\n";
    echo "密码哈希: " . $user->password . "\n";
    
    // 测试密码验证
    echo "\n=== 测试密码验证 ===\n";
    $isValid = Hash::check('admin123', $user->password);
    echo "密码 'admin123' 验证结果: " . ($isValid ? '正确' : '错误') . "\n";
    
    // 获取用户角色
    echo "\n=== 用户角色 ===\n";
    $roles = $user->roles;
    if ($roles->count() > 0) {
        foreach ($roles as $role) {
            echo "角色: " . $role->name . " (" . $role->display_name . ")\n";
        }
    } else {
        echo "用户没有分配角色\n";
    }
} else {
    echo "管理员用户未找到\n";
    
    // 检查所有用户
    echo "\n=== 所有用户 ===\n";
    $users = App\Models\User::all();
    foreach ($users as $u) {
        echo "用户: " . $u->username . " (ID: " . $u->id . ")\n";
    }
} 