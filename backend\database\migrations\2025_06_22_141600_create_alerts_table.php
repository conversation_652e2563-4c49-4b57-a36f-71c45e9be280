<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('alerts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('alert_rule_id')->constrained('alert_rules')->onDelete('cascade')->comment('预警规则ID');
            $table->foreignId('monitoring_task_id')->constrained('monitoring_tasks')->onDelete('cascade')->comment('监控任务ID');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade')->comment('用户ID');
            
            // 预警基本信息
            $table->string('title', 200)->comment('预警标题');
            $table->text('message')->comment('预警消息');
            $table->string('severity', 20)->comment('严重级别：low,medium,high,critical');
            $table->string('priority', 20)->comment('优先级：low,normal,high,urgent');
            
            // 触发数据
            $table->json('trigger_data')->comment('触发数据快照');
            $table->json('context_data')->nullable()->comment('上下文数据');
            $table->string('trigger_field', 100)->comment('触发字段');
            $table->string('trigger_value', 255)->comment('触发值');
            $table->string('threshold_value', 255)->comment('阈值');
            $table->string('operator', 20)->comment('操作符');
            
            // 商品信息
            $table->string('product_id', 50)->nullable()->comment('商品ID');
            $table->string('sku_id', 50)->nullable()->comment('SKU ID');
            $table->text('product_title')->nullable()->comment('商品标题');
            
            // 状态管理
            $table->string('status', 20)->default('pending')->comment('处理状态：pending,processing,resolved,ignored');
            $table->timestamp('resolved_at')->nullable()->comment('解决时间');
            $table->foreignId('resolved_by')->nullable()->constrained('users')->onDelete('set null')->comment('解决人');
            $table->text('resolution_note')->nullable()->comment('解决备注');
            
            // 通知状态
            $table->json('notification_status')->nullable()->comment('通知发送状态');
            $table->integer('notification_attempts')->default(0)->comment('通知尝试次数');
            $table->timestamp('last_notification_at')->nullable()->comment('最后通知时间');
            
            // 统计信息
            $table->integer('view_count')->default(0)->comment('查看次数');
            $table->timestamp('first_viewed_at')->nullable()->comment('首次查看时间');
            $table->timestamp('last_viewed_at')->nullable()->comment('最后查看时间');
            
            $table->timestamps();
            
            // 索引
            $table->index(['alert_rule_id', 'created_at']);
            $table->index(['monitoring_task_id', 'created_at']);
            $table->index(['user_id', 'status']);
            $table->index(['severity', 'priority']);
            $table->index(['status', 'created_at']);
            $table->index(['product_id', 'sku_id']);
            $table->index(['resolved_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('alerts');
    }
};
