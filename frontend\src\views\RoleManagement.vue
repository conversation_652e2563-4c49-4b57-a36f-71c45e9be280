<template>
  <div class="role-management">
    <div class="page-header">
      <h1>角色与权限管理</h1>
      <p>管理系统角色和权限分配</p>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" :icon="Plus" @click="showCreateDialog = true">
          添加角色
        </el-button>
      </div>
    </div>

    <!-- 角色列表 -->
    <div class="role-grid">
      <el-card
        v-for="role in roles"
        :key="role.id"
        class="role-card"
        shadow="hover"
      >
        <template #header>
          <div class="role-header">
            <div class="role-info">
              <h3>{{ role.display_name }}</h3>
              <p class="role-name">{{ role.name }}</p>
            </div>
            <div class="role-actions">
              <el-dropdown @command="(command) => handleRoleAction(command, role)">
                <el-button :icon="MoreFilled" circle />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">编辑角色</el-dropdown-item>
                    <el-dropdown-item command="permissions">管理权限</el-dropdown-item>
                    <el-dropdown-item command="delete" divided v-if="role.name !== 'admin'">
                      删除角色
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </template>

        <div class="role-content">
          <p class="role-description">{{ role.description }}</p>
          
          <div class="role-stats">
            <div class="stat-item">
              <span class="stat-label">用户数量</span>
              <span class="stat-value">{{ role.user_count || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">权限数量</span>
              <span class="stat-value">{{ role.permissions?.length || 0 }}</span>
            </div>
          </div>

          <div class="role-permissions">
            <h4>权限列表</h4>
            <div class="permission-tags">
              <el-tag
                v-for="permission in role.permissions?.slice(0, 6)"
                :key="permission.id"
                size="small"
                style="margin-right: 4px; margin-bottom: 4px"
              >
                {{ permission.display_name }}
              </el-tag>
              <el-tag
                v-if="role.permissions?.length > 6"
                size="small"
                type="info"
              >
                +{{ role.permissions.length - 6 }} 更多
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 创建/编辑角色对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingRole ? '编辑角色' : '添加角色'"
      width="500px"
      @close="resetForm"
    >
      <el-form
        ref="roleFormRef"
        :model="roleForm"
        :rules="roleFormRules"
        label-width="100px"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input 
            v-model="roleForm.name" 
            placeholder="请输入角色名称（英文）"
            :disabled="editingRole && editingRole.name === 'admin'"
          />
        </el-form-item>
        <el-form-item label="显示名称" prop="display_name">
          <el-input v-model="roleForm.display_name" placeholder="请输入显示名称" />
        </el-form-item>
        <el-form-item label="角色描述" prop="description">
          <el-input
            v-model="roleForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入角色描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveRole" :loading="saving">
          {{ editingRole ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 权限管理对话框 -->
    <el-dialog v-model="showPermissionDialog" title="管理角色权限" width="800px">
      <div class="permission-management">
        <p>为角色 <strong>{{ selectedRole?.display_name }}</strong> 分配权限：</p>
        
        <!-- 权限分组 -->
        <div class="permission-groups">
          <div
            v-for="group in permissionGroups"
            :key="group.name"
            class="permission-group"
          >
            <div class="group-header">
              <el-checkbox
                :model-value="isGroupSelected(group)"
                :indeterminate="isGroupIndeterminate(group)"
                @change="(checked) => handleGroupCheck(group, checked)"
              >
                <strong>{{ group.display_name }}</strong>
              </el-checkbox>
              <span class="group-description">{{ group.description }}</span>
            </div>
            
            <div class="group-permissions">
              <el-checkbox
                v-for="permission in group.permissions"
                :key="permission.id"
                v-model="selectedPermissionIds"
                :label="permission.id"
                class="permission-item"
              >
                <div class="permission-info">
                  <span class="permission-name">{{ permission.display_name }}</span>
                  <span class="permission-desc">{{ permission.description }}</span>
                </div>
              </el-checkbox>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="showPermissionDialog = false">取消</el-button>
        <el-button type="primary" @click="saveRolePermissions" :loading="saving">
          保存权限
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, MoreFilled } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const saving = ref(false)

// 对话框状态
const showCreateDialog = ref(false)
const showPermissionDialog = ref(false)
const editingRole = ref(null)
const selectedRole = ref(null)

// 表单数据
const roleForm = ref({
  name: '',
  display_name: '',
  description: ''
})

const selectedPermissionIds = ref([])

// 表单验证规则
const roleFormRules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '角色名称只能包含字母、数字和下划线，且以字母或下划线开头', trigger: 'blur' }
  ],
  display_name: [
    { required: true, message: '请输入显示名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入角色描述', trigger: 'blur' }
  ]
}

// 模拟数据
const roles = ref([
  {
    id: 1,
    name: 'admin',
    display_name: '系统管理员',
    description: '拥有系统所有权限，可以管理用户、角色、数据源等',
    user_count: 1,
    permissions: [
      { id: 1, name: 'user.view', display_name: '查看用户' },
      { id: 2, name: 'user.create', display_name: '创建用户' },
      { id: 3, name: 'user.edit', display_name: '编辑用户' },
      { id: 4, name: 'user.delete', display_name: '删除用户' },
      { id: 5, name: 'role.view', display_name: '查看角色' },
      { id: 6, name: 'role.create', display_name: '创建角色' },
      { id: 7, name: 'role.edit', display_name: '编辑角色' },
      { id: 8, name: 'role.delete', display_name: '删除角色' },
      { id: 9, name: 'datasource.view', display_name: '查看数据源' },
      { id: 10, name: 'datasource.create', display_name: '创建数据源' }
    ]
  },
  {
    id: 2,
    name: 'user',
    display_name: '普通用户',
    description: '只能使用基本的监控和查看功能',
    user_count: 5,
    permissions: [
      { id: 11, name: 'monitoring.view', display_name: '查看监控' },
      { id: 12, name: 'monitoring.create', display_name: '创建监控任务' },
      { id: 13, name: 'alert.view', display_name: '查看预警' },
      { id: 14, name: 'dashboard.view', display_name: '查看看板' }
    ]
  }
])

const permissionGroups = ref([
  {
    name: 'user',
    display_name: '用户管理',
    description: '用户账号相关权限',
    permissions: [
      { id: 1, name: 'user.view', display_name: '查看用户', description: '查看用户列表和详情' },
      { id: 2, name: 'user.create', display_name: '创建用户', description: '添加新用户' },
      { id: 3, name: 'user.edit', display_name: '编辑用户', description: '修改用户信息' },
      { id: 4, name: 'user.delete', display_name: '删除用户', description: '删除用户账号' }
    ]
  },
  {
    name: 'role',
    display_name: '角色管理',
    description: '角色和权限相关功能',
    permissions: [
      { id: 5, name: 'role.view', display_name: '查看角色', description: '查看角色列表和权限' },
      { id: 6, name: 'role.create', display_name: '创建角色', description: '添加新角色' },
      { id: 7, name: 'role.edit', display_name: '编辑角色', description: '修改角色信息和权限' },
      { id: 8, name: 'role.delete', display_name: '删除角色', description: '删除角色' }
    ]
  },
  {
    name: 'datasource',
    display_name: '数据源管理',
    description: '数据源配置相关权限',
    permissions: [
      { id: 9, name: 'datasource.view', display_name: '查看数据源', description: '查看数据源配置' },
      { id: 10, name: 'datasource.create', display_name: '创建数据源', description: '添加新数据源' },
      { id: 15, name: 'datasource.edit', display_name: '编辑数据源', description: '修改数据源配置' },
      { id: 16, name: 'datasource.delete', display_name: '删除数据源', description: '删除数据源' }
    ]
  },
  {
    name: 'monitoring',
    display_name: '监控管理',
    description: '监控任务相关功能',
    permissions: [
      { id: 11, name: 'monitoring.view', display_name: '查看监控', description: '查看监控任务和数据' },
      { id: 12, name: 'monitoring.create', display_name: '创建监控任务', description: '创建新的监控任务' },
      { id: 17, name: 'monitoring.edit', display_name: '编辑监控任务', description: '修改监控任务配置' },
      { id: 18, name: 'monitoring.delete', display_name: '删除监控任务', description: '删除监控任务' }
    ]
  },
  {
    name: 'alert',
    display_name: '预警管理',
    description: '预警规则和通知相关功能',
    permissions: [
      { id: 13, name: 'alert.view', display_name: '查看预警', description: '查看预警规则和历史' },
      { id: 19, name: 'alert.create', display_name: '创建预警规则', description: '设置新的预警规则' },
      { id: 20, name: 'alert.edit', display_name: '编辑预警规则', description: '修改预警规则' },
      { id: 21, name: 'alert.delete', display_name: '删除预警规则', description: '删除预警规则' }
    ]
  },
  {
    name: 'dashboard',
    display_name: '数据看板',
    description: '数据查看和分析相关功能',
    permissions: [
      { id: 14, name: 'dashboard.view', display_name: '查看看板', description: '查看数据看板和报表' },
      { id: 22, name: 'dashboard.export', display_name: '导出数据', description: '导出看板数据' }
    ]
  }
])

// 计算属性
const isGroupSelected = (group) => {
  const groupPermissionIds = group.permissions.map(p => p.id)
  return groupPermissionIds.every(id => selectedPermissionIds.value.includes(id))
}

const isGroupIndeterminate = (group) => {
  const groupPermissionIds = group.permissions.map(p => p.id)
  const selectedCount = groupPermissionIds.filter(id => selectedPermissionIds.value.includes(id)).length
  return selectedCount > 0 && selectedCount < groupPermissionIds.length
}

// 方法
const handleRoleAction = (command, role) => {
  switch (command) {
    case 'edit':
      editRole(role)
      break
    case 'permissions':
      managePermissions(role)
      break
    case 'delete':
      deleteRole(role)
      break
  }
}

const editRole = (role) => {
  editingRole.value = role
  roleForm.value = {
    name: role.name,
    display_name: role.display_name,
    description: role.description
  }
  showCreateDialog.value = true
}

const managePermissions = (role) => {
  selectedRole.value = role
  selectedPermissionIds.value = role.permissions?.map(p => p.id) || []
  showPermissionDialog.value = true
}

const deleteRole = async (role) => {
  try {
    await ElMessageBox.confirm(`确定要删除角色 "${role.display_name}" 吗？`, '确认删除', {
      type: 'warning'
    })
    
    // TODO: 调用API删除角色
    const index = roles.value.findIndex(r => r.id === role.id)
    if (index > -1) {
      roles.value.splice(index, 1)
    }
    
    ElMessage.success('删除成功')
  } catch (error) {
    // 用户取消
  }
}

const handleGroupCheck = (group, checked) => {
  const groupPermissionIds = group.permissions.map(p => p.id)
  if (checked) {
    // 选中组内所有权限
    groupPermissionIds.forEach(id => {
      if (!selectedPermissionIds.value.includes(id)) {
        selectedPermissionIds.value.push(id)
      }
    })
  } else {
    // 取消选中组内所有权限
    selectedPermissionIds.value = selectedPermissionIds.value.filter(
      id => !groupPermissionIds.includes(id)
    )
  }
}

const saveRole = async () => {
  saving.value = true
  try {
    if (editingRole.value) {
      // 更新角色
      const index = roles.value.findIndex(r => r.id === editingRole.value.id)
      if (index > -1) {
        roles.value[index] = {
          ...roles.value[index],
          ...roleForm.value
        }
      }
      ElMessage.success('角色更新成功')
    } else {
      // 创建角色
      const newRole = {
        id: Date.now(),
        ...roleForm.value,
        user_count: 0,
        permissions: []
      }
      roles.value.push(newRole)
      ElMessage.success('角色创建成功')
    }
    showCreateDialog.value = false
    resetForm()
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    saving.value = false
  }
}

const saveRolePermissions = async () => {
  saving.value = true
  try {
    // TODO: 调用API更新角色权限
    const roleIndex = roles.value.findIndex(r => r.id === selectedRole.value.id)
    if (roleIndex > -1) {
      const allPermissions = permissionGroups.value.flatMap(group => group.permissions)
      roles.value[roleIndex].permissions = allPermissions.filter(p => 
        selectedPermissionIds.value.includes(p.id)
      )
    }
    
    ElMessage.success('权限更新成功')
    showPermissionDialog.value = false
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    saving.value = false
  }
}

const resetForm = () => {
  roleForm.value = {
    name: '',
    display_name: '',
    description: ''
  }
  editingRole.value = null
}

const loadRoles = async () => {
  loading.value = true
  try {
    // TODO: 调用API加载角色列表
  } catch (error) {
    ElMessage.error('加载角色列表失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadRoles()
})
</script>

<style scoped lang="scss">
.role-management {
  .page-header {
    margin-bottom: 24px;
    
    h1 {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #303133;
    }
    
    p {
      color: #606266;
      margin: 0;
    }
  }
  
  .toolbar {
    margin-bottom: 16px;
  }
  
  .role-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
    
    .role-card {
      .role-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        
        .role-info {
          h3 {
            margin: 0 0 4px 0;
            font-size: 18px;
            font-weight: 600;
            color: #303133;
          }
          
          .role-name {
            font-size: 12px;
            color: #909399;
            background: #f5f7fa;
            padding: 2px 8px;
            border-radius: 4px;
            display: inline-block;
          }
        }
      }
      
      .role-content {
        .role-description {
          color: #606266;
          margin-bottom: 16px;
          line-height: 1.5;
        }
        
        .role-stats {
          display: flex;
          gap: 20px;
          margin-bottom: 16px;
          
          .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            
            .stat-label {
              font-size: 12px;
              color: #909399;
              margin-bottom: 4px;
            }
            
            .stat-value {
              font-size: 20px;
              font-weight: 600;
              color: #409eff;
            }
          }
        }
        
        .role-permissions {
          h4 {
            margin: 0 0 8px 0;
            font-size: 14px;
            color: #303133;
          }
          
          .permission-tags {
            min-height: 24px;
          }
        }
      }
    }
  }
  
  .permission-management {
    .permission-groups {
      margin-top: 20px;
      
      .permission-group {
        margin-bottom: 24px;
        border: 1px solid #ebeef5;
        border-radius: 6px;
        
        .group-header {
          padding: 12px 16px;
          background: #fafafa;
          border-bottom: 1px solid #ebeef5;
          
          .group-description {
            font-size: 12px;
            color: #909399;
            margin-left: 8px;
          }
        }
        
        .group-permissions {
          padding: 16px;
          
          .permission-item {
            display: block;
            margin-bottom: 12px;
            
            .permission-info {
              display: flex;
              flex-direction: column;
              margin-left: 8px;
              
              .permission-name {
                font-weight: 500;
                color: #303133;
              }
              
              .permission-desc {
                font-size: 12px;
                color: #909399;
                margin-top: 2px;
              }
            }
          }
        }
      }
    }
  }
}
</style> 