<template>
  <div class="data-source-management">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">数据源管理</h1>
        <p class="page-description">管理系统的数据源配置，包括API接口、数据库连接等</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog" :icon="Plus">
          新增数据源
        </el-button>
        <el-button @click="refreshList" :icon="Refresh" :loading="loading">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-bar">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索数据源名称或描述"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.type"
            placeholder="数据源类型"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="type in dataSourceTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.status"
            placeholder="状态"
            clearable
            @change="handleSearch"
          >
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="inactive" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="danger" @click="handleBatchDelete" :disabled="selectedRows.length === 0">
            批量删除 ({{ selectedRows.length }})
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 数据源列表 -->
    <div class="data-table">
      <el-table
        v-loading="loading"
        :data="dataSourceList"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="数据源名称" min-width="150">
          <template #default="{ row }">
            <div class="source-name">
              <el-icon class="source-icon" :color="getTypeColor(row.type)">
                <component :is="getTypeIcon(row.type)" />
              </el-icon>
              <span>{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">
              {{ getTypeName(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="url" label="连接地址" min-width="200" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              active-value="active"
              inactive-value="inactive"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="updated_at" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="testConnection(row)" :loading="row.testing">
              测试连接
            </el-button>
            <el-button size="small" type="primary" @click="showEditDialog(row)">
              编辑
            </el-button>
            <el-button size="small" type="info" @click="duplicateDataSource(row)">
              复制
            </el-button>
            <el-popconfirm
              title="确定要删除这个数据源吗？"
              @confirm="deleteDataSource(row.id)"
            >
              <template #reference>
                <el-button size="small" type="danger">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.current_page"
          v-model:page-size="pagination.per_page"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="数据源名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入数据源名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据源类型" prop="type">
              <el-select v-model="formData.type" placeholder="请选择数据源类型" style="width: 100%">
                <el-option
                  v-for="type in dataSourceTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="2"
            placeholder="请输入数据源描述"
          />
        </el-form-item>

        <el-form-item label="连接地址" prop="url">
          <el-input v-model="formData.url" placeholder="请输入连接地址或API端点" />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input v-model="formData.username" placeholder="用户名（可选）" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input
                v-model="formData.password"
                type="password"
                placeholder="密码（可选）"
                show-password
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="formData.status">
                <el-radio label="active">启用</el-radio>
                <el-radio label="inactive">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采集间隔" prop="collection_interval">
              <el-input-number
                v-model="formData.collection_interval"
                :min="1"
                :max="86400"
                placeholder="秒"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 参数映射 JSON 编辑器 -->
        <el-form-item label="参数映射">
          <div class="json-editor-container">
            <div class="json-editor-header">
              <span>参数映射配置（JSON格式）</span>
              <el-button size="small" @click="formatJson('param_mapping')">格式化</el-button>
            </div>
            <el-input
              v-model="formData.param_mapping"
              type="textarea"
              :rows="8"
              placeholder="请输入参数映射配置，例如：&#10;{&#10;  &quot;api_key&quot;: &quot;your_api_key&quot;,&#10;  &quot;timeout&quot;: 30&#10;}"
            />
          </div>
        </el-form-item>

        <!-- 字段映射 JSON 编辑器 -->
        <el-form-item label="字段映射">
          <div class="json-editor-container">
            <div class="json-editor-header">
              <span>字段映射配置（JSON格式）</span>
              <el-button size="small" @click="formatJson('field_mapping')">格式化</el-button>
            </div>
            <el-input
              v-model="formData.field_mapping"
              type="textarea"
              :rows="8"
              placeholder="请输入字段映射配置，例如：&#10;{&#10;  &quot;product_name&quot;: &quot;$.data.name&quot;,&#10;  &quot;price&quot;: &quot;$.data.price&quot;&#10;}"
            />
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Refresh,
  Search,
  Link,
  DataLine,
  Setting,
  Delete,
  Edit,
  CopyDocument,
  CaretRight,
} from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'

// 类型定义
interface DataSource {
  id: number
  name: string
  type: string
  description: string
  url: string
  username?: string
  password?: string
  status: 'active' | 'inactive'
  param_mapping?: string
  field_mapping?: string
  collection_interval: number
  created_at: string
  updated_at: string
  testing?: boolean
}

interface SearchForm {
  search: string
  type: string
  status: string
}

interface Pagination {
  current_page: number
  per_page: number
  total: number
}

// 响应式数据
const authStore = useAuthStore()
const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

const dataSourceList = ref<DataSource[]>([])
const selectedRows = ref<DataSource[]>([])
const dataSourceTypes = ref([])

const searchForm = reactive<SearchForm>({
  search: '',
  type: '',
  status: ''
})

const pagination = reactive<Pagination>({
  current_page: 1,
  per_page: 20,
  total: 0
})

const formData = reactive({
  id: null,
  name: '',
  type: '',
  description: '',
  url: '',
  username: '',
  password: '',
  status: 'active',
  param_mapping: '{}',
  field_mapping: '{}',
  collection_interval: 300
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入数据源名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择数据源类型', trigger: 'change' }
  ],
  url: [
    { required: true, message: '请输入连接地址', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL地址', trigger: 'blur' }
  ],
  collection_interval: [
    { required: true, message: '请输入采集间隔', trigger: 'blur' },
    { type: 'number', min: 1, max: 86400, message: '采集间隔必须在1-86400秒之间', trigger: 'blur' }
  ]
}

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑数据源' : '新增数据源')

// 页面初始化
onMounted(() => {
  loadDataSourceTypes()
  loadDataSources()
})

// 加载数据源类型
async function loadDataSourceTypes() {
  try {
    const response = await authStore.request('/api/data-sources/types')
    if (response.success) {
      dataSourceTypes.value = response.data
    }
  } catch (error) {
    console.error('加载数据源类型失败:', error)
  }
}

// 加载数据源列表
async function loadDataSources() {
  loading.value = true
  try {
    const params = new URLSearchParams({
      page: pagination.current_page.toString(),
      per_page: pagination.per_page.toString(),
      ...searchForm
    })

    const response = await authStore.request(`/api/data-sources?${params}`)
    if (response.success) {
      dataSourceList.value = response.data.data
      pagination.total = response.data.total
      pagination.current_page = response.data.current_page
    } else {
      ElMessage.error(response.message || '加载数据源列表失败')
    }
  } catch (error) {
    ElMessage.error('加载数据源列表失败')
    console.error('加载数据源列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 刷新列表
function refreshList() {
  loadDataSources()
}

// 搜索处理
function handleSearch() {
  pagination.current_page = 1
  loadDataSources()
}

// 重置搜索
function resetSearch() {
  Object.assign(searchForm, {
    search: '',
    type: '',
    status: ''
  })
  handleSearch()
}

// 分页处理
function handlePageSizeChange(size: number) {
  pagination.per_page = size
  pagination.current_page = 1
  loadDataSources()
}

function handleCurrentChange(page: number) {
  pagination.current_page = page
  loadDataSources()
}

// 选择行处理
function handleSelectionChange(selection: DataSource[]) {
  selectedRows.value = selection
}

// 显示创建对话框
function showCreateDialog() {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 显示编辑对话框
function showEditDialog(row: DataSource) {
  isEdit.value = true
  Object.assign(formData, {
    ...row,
    param_mapping: row.param_mapping || '{}',
    field_mapping: row.field_mapping || '{}'
  })
  dialogVisible.value = true
}

// 重置表单
function resetForm() {
  Object.assign(formData, {
    id: null,
    name: '',
    type: '',
    description: '',
    url: '',
    username: '',
    password: '',
    status: 'active',
    param_mapping: '{}',
    field_mapping: '{}',
    collection_interval: 300
  })
  formRef.value?.clearValidate()
}

// 提交表单
async function handleSubmit() {
  try {
    await formRef.value.validate()
    
    // 验证JSON格式
    try {
      JSON.parse(formData.param_mapping)
      JSON.parse(formData.field_mapping)
    } catch (error) {
      ElMessage.error('JSON格式不正确，请检查参数映射和字段映射')
      return
    }

    submitting.value = true
    
    const url = isEdit.value ? `/api/data-sources/${formData.id}` : '/api/data-sources'
    const method = isEdit.value ? 'PUT' : 'POST'
    
    const response = await authStore.request(url, {
      method,
      body: JSON.stringify(formData)
    })

    if (response.success) {
      ElMessage.success(isEdit.value ? '数据源更新成功' : '数据源创建成功')
      dialogVisible.value = false
      loadDataSources()
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitting.value = false
  }
}

// 状态变更
async function handleStatusChange(row: DataSource) {
  try {
    const response = await authStore.request(`/api/data-sources/${row.id}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status: row.status })
    })

    if (response.success) {
      ElMessage.success('状态更新成功')
      loadDataSources()
    } else {
      ElMessage.error(response.message || '状态更新失败')
      // 恢复原状态
      row.status = row.status === 'active' ? 'inactive' : 'active'
    }
  } catch (error) {
    ElMessage.error('状态更新失败')
    row.status = row.status === 'active' ? 'inactive' : 'active'
  }
}

// 测试连接
async function testConnection(row: DataSource) {
  row.testing = true
  try {
    const response = await authStore.request(`/api/data-sources/${row.id}/test`, {
      method: 'POST'
    })

    if (response.success) {
      ElMessage.success('连接测试成功')
    } else {
      ElMessage.error(response.message || '连接测试失败')
    }
  } catch (error) {
    ElMessage.error('连接测试失败')
  } finally {
    row.testing = false
  }
}

// 复制数据源
async function duplicateDataSource(row: DataSource) {
  try {
    const response = await authStore.request(`/api/data-sources/${row.id}/duplicate`, {
      method: 'POST'
    })

    if (response.success) {
      ElMessage.success('数据源复制成功')
      loadDataSources()
    } else {
      ElMessage.error(response.message || '数据源复制失败')
    }
  } catch (error) {
    ElMessage.error('数据源复制失败')
  }
}

// 删除数据源
async function deleteDataSource(id: number) {
  try {
    const response = await authStore.request(`/api/data-sources/${id}`, {
      method: 'DELETE'
    })

    if (response.success) {
      ElMessage.success('数据源删除成功')
      loadDataSources()
    } else {
      ElMessage.error(response.message || '数据源删除失败')
    }
  } catch (error) {
    ElMessage.error('数据源删除失败')
  }
}

// 批量删除
async function handleBatchDelete() {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据源')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 个数据源吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const ids = selectedRows.value.map(row => row.id)
    const response = await authStore.request('/api/data-sources', {
      method: 'DELETE',
      body: JSON.stringify({ ids })
    })

    if (response.success) {
      ElMessage.success(`成功删除 ${response.data.deleted_count} 个数据源`)
      loadDataSources()
    } else {
      ElMessage.error(response.message || '批量删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 格式化JSON
function formatJson(field: 'param_mapping' | 'field_mapping') {
  try {
    const parsed = JSON.parse(formData[field])
    formData[field] = JSON.stringify(parsed, null, 2)
  } catch (error) {
    ElMessage.error('JSON格式不正确，无法格式化')
  }
}

// 工具函数
function getTypeIcon(type: string) {
  const iconMap: Record<string, any> = {
    api: Link,
    database: DataLine,
    web: Link,
    file: Link
  }
  return iconMap[type] || Link
}

function getTypeColor(type: string) {
  const colorMap: Record<string, string> = {
    api: '#409EFF',
    database: '#67C23A',
    web: '#E6A23C',
    file: '#909399'
  }
  return colorMap[type] || '#909399'
}

function getTypeTagType(type: string) {
  const typeMap: Record<string, string> = {
    api: 'primary',
    database: 'success',
    web: 'warning',
    file: 'info'
  }
  return typeMap[type] || 'info'
}

function getTypeName(type: string) {
  const nameMap: Record<string, string> = {
    api: 'API接口',
    database: '数据库',
    web: '网页抓取',
    file: '文件导入'
  }
  return nameMap[type] || type
}

function formatDateTime(dateTime: string) {
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style scoped>
.data-source-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-description {
  color: #606266;
  margin: 0;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.search-bar {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.data-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.source-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.source-icon {
  font-size: 16px;
}

.pagination {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
}

.json-editor-container {
  width: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.json-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
  font-size: 12px;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 