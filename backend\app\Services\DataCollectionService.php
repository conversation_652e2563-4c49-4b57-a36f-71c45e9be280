<?php

namespace App\Services;

use App\Jobs\ProcessAlertChecking;
use App\Models\DataSource;
use App\Models\ProductData;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class DataCollectionService
{

    /**
     * 执行数据采集和标准化
     *
     * @param int $dataSourceId 数据源ID
     * @param mixed $itemId 目标商品ID
     * @param array $extraParams 额外参数
     * @return array
     */
    public function collectAndStandardize(int $dataSourceId, $itemId, array $extraParams = []): array
    {
        Log::info('开始数据采集与标准化', [
            'data_source_id' => $dataSourceId,
            'item_id' => $itemId,
        ]);

        try {
            // 1. 获取数据源配置
            $dataSource = DataSource::with('fieldMappings')->findOrFail($dataSourceId);

            // 2. 构造API请求
            $requestData = $this->buildApiRequest($dataSource, $itemId, $extraParams);

            // 3. 发起HTTP请求
            $response = $this->makeHttpRequest($requestData);

            // 4. 解析响应
            $rawData = $this->parseResponse($response);

            // 5. 提取和标准化数据
            $standardizedData = $this->standardizeData($rawData, $dataSource->fieldMappings);

            // 6. 存储数据
            $productData = $this->storeData($dataSource, $itemId, $rawData, $standardizedData);

            // 7. 异步处理预警检查
            $this->dispatchAlertChecking($productData);

            Log::info('数据采集与标准化成功', [
                'product_data_id' => $productData->id,
                'alert_check_dispatched' => true,
            ]);

            return [
                'success' => true,
                'product_data_id' => $productData->id,
                'standardized_data' => $standardizedData,
                'alert_check_dispatched' => true,
            ];

        } catch (\Exception $e) {
            Log::error('数据采集与标准化失败', [
                'data_source_id' => $dataSourceId,
                'item_id' => $itemId,
                'error' => $e->getMessage(),
                'trace' => Str::limit($e->getTraceAsString(), 1000)
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * 构造API请求
     */
    private function buildApiRequest(DataSource $dataSource, $itemId, array $extraParams): array
    {
        // 动态替换URL中的占位符
        $endpoint = str_replace('{item_id}', $itemId, $dataSource->api_endpoint_template ?? '');
        $url = $dataSource->getFullApiUrl($endpoint);

        // 合并参数
        $params = $dataSource->getRequestParams($extraParams);

        // 构造请求数据
        return [
            'method' => strtoupper($dataSource->api_method ?? 'GET'),
            'url' => $url,
            'headers' => $dataSource->getRequestHeaders(),
            'params' => $params,
            'timeout' => $dataSource->timeout,
        ];
    }

    /**
     * 发起HTTP请求
     */
    private function makeHttpRequest(array $requestData): \Illuminate\Http\Client\Response
    {
        $response = Http::withHeaders($requestData['headers'])
            ->timeout($requestData['timeout']);
        
        // 解决SSL证书问题
        // 在生产环境中，应该配置php.ini指向正确的cacert.pem文件
        // 这里为了开发方便，暂时禁用SSL验证
        if (app()->environment('local')) {
            $response->withoutVerifying();
        }

        switch ($requestData['method']) {
            case 'POST':
                return $response->post($requestData['url'], $requestData['params']);
            case 'PUT':
                return $response->put($requestData['url'], $requestData['params']);
            case 'PATCH':
                return $response->patch($requestData['url'], $requestData['params']);
            case 'DELETE':
                return $response->delete($requestData['url'], $requestData['params']);
            default: // GET
                return $response->get($requestData['url'], $requestData['params']);
        }
    }

    /**
     * 解析HTTP响应
     */
    private function parseResponse(\Illuminate\Http\Client\Response $response): array
    {
        if (!$response->successful()) {
            throw new \Exception("API请求失败: HTTP {$response->status()} - " . $response->body());
        }

        $data = $response->json();

        if (is_null($data)) {
            throw new \Exception("无法解析JSON响应: " . $response->body());
        }

        return $data;
    }

    /**
     * 根据字段映射标准化数据
     *
     * @param array $rawData
     * @param \Illuminate\Database\Eloquent\Collection $mappings
     * @return array
     */
    private function standardizeData(array $rawData, $mappings): array
    {
        $standardizedData = [];

        foreach ($mappings as $mapping) {
            // 使用JSON Path (dot notation)提取数据
            $value = Arr::get($rawData, $mapping->source_field);

            // 检查必需字段
            if (is_null($value) && $mapping->is_required) {
                // 如果有默认值，则使用默认值
                if (!is_null($mapping->default_value)) {
                    $value = $mapping->default_value;
                } else {
                    throw new \Exception("必需字段 '{$mapping->target_field}' ({$mapping->source_field}) 的值为null，且没有默认值");
                }
            }

            // 如果值不为null，则进行转换
            if (!is_null($value)) {
                $value = $this->transformValue($value, $mapping->transform_rule, $mapping->field_type);
            }

            $standardizedData[$mapping->target_field] = $value;
        }

        return $standardizedData;
    }

    /**
     * 转换单个值
     */
    private function transformValue($value, ?array $transformRule, string $fieldType)
    {
        // 1. 类型转换
        switch ($fieldType) {
            case 'integer':
                $value = (int) $value;
                break;
            case 'float':
                $value = (float) $value;
                break;
            case 'boolean':
                // 处理 'true', 'false', 1, 0 等情况
                $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                break;
            case 'string':
                $value = (string) $value;
                break;
        }

        // 2. 自定义转换规则
        if (!empty($transformRule)) {
            foreach ($transformRule as $rule => $options) {
                switch ($rule) {
                    case 'min_from_array': // 例如: '最低到手价'
                        if (is_array($value)) {
                            $value = min(array_filter($value, 'is_numeric'));
                        }
                        break;
                    case 'regex_match':
                        if (is_string($value) && isset($options['pattern'])) {
                            preg_match($options['pattern'], $value, $matches);
                            $value = $matches[$options['index'] ?? 0] ?? null;
                        }
                        break;
                    case 'string_replace':
                        if (is_string($value) && isset($options['search'], $options['replace'])) {
                            $value = str_replace($options['search'], $options['replace'], $value);
                        }
                        break;
                    case 'timestamp_to_date':
                        $value = \Carbon\Carbon::createFromTimestamp($value)->format($options['format'] ?? 'Y-m-d H:i:s');
                        break;
                    // 可以添加更多自定义规则...
                }
            }
        }

        return $value;
    }

    /**
     * 存储数据到数据库
     */
    private function storeData(DataSource $dataSource, $itemId, array $rawData, array $standardizedData): ProductData
    {
        return ProductData::updateOrCreate(
            [
                'data_source_id' => $dataSource->id,
                'item_id' => $itemId,
            ],
            [
                'raw_data' => $rawData,
                'standardized_data' => $standardizedData,
                'last_collected_at' => now(),
            ]
        );
    }

    /**
     * 分发预警检查任务到队列
     *
     * @param ProductData $productData
     * @return void
     */
    private function dispatchAlertChecking(ProductData $productData): void
    {
        try {
            ProcessAlertChecking::dispatch($productData->id)
                ->onQueue('alerts') // 使用专门的队列处理预警
                ->delay(now()->addSeconds(5)); // 延迟5秒执行，确保数据已完全保存

            Log::debug('预警检查任务已分发到队列', [
                'product_data_id' => $productData->id,
            ]);
        } catch (\Exception $e) {
            Log::error('分发预警检查任务失败', [
                'product_data_id' => $productData->id,
                'error' => $e->getMessage(),
            ]);
        }
    }
} 