<template>
  <div class="task-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">监控任务管理</h1>
        <p class="page-description">管理您的监控任务和任务组，创建和配置数据监控计划</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateTaskDialog" :icon="Plus">
          新建监控任务
        </el-button>
        <el-button @click="showCreateGroupDialog" :icon="FolderAdd">
          新建任务组
        </el-button>
        <el-button @click="refreshData" :icon="Refresh" :loading="loading">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 任务组和任务列表 -->
    <div class="task-content">
      <!-- 左侧任务组列表 -->
      <div class="task-groups">
        <div class="section-header">
          <h3>任务组</h3>
          <el-button size="small" text @click="showCreateGroupDialog" :icon="Plus">
            添加组
          </el-button>
        </div>
        
        <div class="group-list" v-loading="groupsLoading">
          <div
            v-for="group in taskGroups"
            :key="group.id"
            :class="['group-item', { active: selectedGroupId === group.id }]"
            @click="selectGroup(group.id)"
          >
            <div class="group-info">
              <div class="group-name">{{ group.name }}</div>
              <div class="group-desc">{{ group.description || '暂无描述' }}</div>
              <div class="group-stats">
                <el-tag size="small">{{ group.monitoring_tasks_count || 0 }} 个任务</el-tag>
              </div>
            </div>
            <div class="group-actions">
              <el-dropdown @command="(cmd) => handleGroupAction(cmd, group)">
                <el-button size="small" text :icon="MoreFilled" />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">编辑</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
          
          <!-- 全部任务选项 -->
          <div
            :class="['group-item', { active: selectedGroupId === null }]"
            @click="selectGroup(null)"
          >
            <div class="group-info">
              <div class="group-name">全部任务</div>
              <div class="group-desc">查看所有监控任务</div>
              <div class="group-stats">
                <el-tag size="small">{{ totalTasksCount }} 个任务</el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧监控任务列表 -->
      <div class="task-list">
        <div class="section-header">
          <h3>{{ selectedGroupName }}</h3>
          <div class="task-filters">
            <el-input
              v-model="searchForm.search"
              placeholder="搜索任务名称"
              :prefix-icon="Search"
              clearable
              @input="handleSearch"
              style="width: 200px; margin-right: 12px;"
            />
            <el-select
              v-model="searchForm.status"
              placeholder="状态"
              clearable
              @change="handleSearch"
              style="width: 120px; margin-right: 12px;"
            >
              <el-option
                v-for="(label, value) in taskStatuses"
                :key="value"
                :label="label"
                :value="value"
              />
            </el-select>
            <el-button @click="showCreateTaskDialog" type="primary" size="small" :icon="Plus">
              新建任务
            </el-button>
          </div>
        </div>

        <!-- 任务表格 -->
        <div class="tasks-table" v-loading="tasksLoading">
          <el-table
            :data="monitoringTasks"
            @selection-change="handleTaskSelectionChange"
            stripe
            style="width: 100%"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="name" label="任务名称" min-width="150">
              <template #default="{ row }">
                <div class="task-name">
                  <span>{{ row.name }}</span>
                  <el-tag v-if="row.auto_start" size="small" type="success">自动启动</el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="data_source" label="数据源" width="120">
              <template #default="{ row }">
                {{ row.data_source?.name || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="frequency_type" label="执行频率" width="120">
              <template #default="{ row }">
                <div>
                  <div>{{ getFrequencyTypeLabel(row.frequency_type) }}</div>
                  <small class="text-muted">
                    {{ getFrequencyDetail(row) }}
                  </small>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusTagType(row.status)">
                  {{ taskStatuses[row.status] || row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="run_count" label="执行次数" width="100" />
            <el-table-column prop="success_rate" label="成功率" width="100">
              <template #default="{ row }">
                {{ row.success_rate || 0 }}%
              </template>
            </el-table-column>
            <el-table-column prop="updated_at" label="更新时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.updated_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button size="small" @click="viewTask(row)">详情</el-button>
                <el-button size="small" type="primary" @click="editTask(row)">编辑</el-button>
                <el-dropdown @command="(cmd) => handleTaskAction(cmd, row)">
                  <el-button size="small" :icon="MoreFilled" />
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="start" v-if="row.status !== 'running'">
                        启动任务
                      </el-dropdown-item>
                      <el-dropdown-item command="pause" v-if="row.status === 'running'">
                        暂停任务
                      </el-dropdown-item>
                      <el-dropdown-item command="stop" v-if="row.status === 'running'">
                        停止任务
                      </el-dropdown-item>
                      <el-dropdown-item command="duplicate">复制任务</el-dropdown-item>
                      <el-dropdown-item command="delete" divided>删除任务</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination">
            <el-pagination
              v-model:current-page="taskPagination.current_page"
              v-model:page-size="taskPagination.per_page"
              :total="taskPagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleTaskPageSizeChange"
              @current-change="handleTaskCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 任务组创建/编辑对话框 -->
    <el-dialog
      v-model="groupDialogVisible"
      :title="groupDialogTitle"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="groupFormRef"
        :model="groupFormData"
        :rules="groupFormRules"
        label-width="100px"
      >
        <el-form-item label="组名称" prop="name">
          <el-input v-model="groupFormData.name" placeholder="请输入任务组名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="groupFormData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入任务组描述"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sort_order">
          <el-input-number
            v-model="groupFormData.sort_order"
            :min="0"
            placeholder="排序值，数字越小越靠前"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="groupDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitGroupForm" :loading="groupSubmitting">
            {{ isEditingGroup ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 监控任务创建/编辑对话框 -->
    <el-dialog
      v-model="taskDialogVisible"
      :title="taskDialogTitle"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="taskFormRef"
        :model="taskFormData"
        :rules="taskFormRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="任务名称" prop="name">
              <el-input v-model="taskFormData.name" placeholder="请输入任务名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务组" prop="task_group_id">
              <el-select
                v-model="taskFormData.task_group_id"
                placeholder="选择任务组（可选）"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="group in taskGroups"
                  :key="group.id"
                  :label="group.name"
                  :value="group.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="taskFormData.description"
            type="textarea"
            :rows="2"
            placeholder="请输入任务描述"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="数据源" prop="data_source_id">
              <el-select
                v-model="taskFormData.data_source_id"
                placeholder="选择数据源"
                style="width: 100%"
                @change="handleDataSourceChange"
              >
                <el-option
                  v-for="source in dataSources"
                  :key="source.id"
                  :label="source.name"
                  :value="source.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="执行频率类型" prop="frequency_type">
              <el-select
                v-model="taskFormData.frequency_type"
                placeholder="选择频率类型"
                style="width: 100%"
                @change="handleFrequencyTypeChange"
              >
                <el-option
                  v-for="(label, value) in frequencyTypes"
                  :key="value"
                  :label="label"
                  :value="value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12" v-if="taskFormData.frequency_type === 'interval'">
            <el-form-item label="执行间隔(分钟)" prop="frequency_value">
              <el-input-number
                v-model="taskFormData.frequency_value"
                :min="1"
                placeholder="执行间隔分钟数"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="taskFormData.frequency_type === 'cron'">
            <el-form-item label="Cron表达式" prop="cron_expression">
              <el-input
                v-model="taskFormData.cron_expression"
                placeholder="如: 0 */30 * * * *"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="目标产品">
          <el-input
            v-model="targetProductsText"
            type="textarea"
            :rows="3"
            placeholder="请输入目标产品ID，每行一个或用逗号分隔"
          />
        </el-form-item>

        <el-form-item label="监控字段">
          <el-input
            v-model="monitorFieldsText"
            type="textarea"
            :rows="2"
            placeholder="请输入要监控的字段名，每行一个或用逗号分隔"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="自动启动">
              <el-switch v-model="taskFormData.auto_start" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="错误通知">
              <el-switch v-model="taskFormData.notify_on_error" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="taskDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitTaskForm" :loading="taskSubmitting">
            {{ isEditingTask ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  FolderAdd,
  Refresh,
  Search,
  MoreFilled
} from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'

const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const groupsLoading = ref(false)
const tasksLoading = ref(false)

// 任务组相关
const taskGroups = ref([])
const selectedGroupId = ref(null)
const totalTasksCount = ref(0)

// 监控任务相关
const monitoringTasks = ref([])
const selectedTasks = ref([])
const dataSources = ref([])

// 搜索表单
const searchForm = reactive({
  search: '',
  status: '',
  task_group_id: null
})

// 分页信息
const taskPagination = reactive({
  current_page: 1,
  per_page: 20,
  total: 0
})

// 任务状态和频率类型
const taskStatuses = ref({})
const frequencyTypes = ref({})

// 任务组对话框
const groupDialogVisible = ref(false)
const isEditingGroup = ref(false)
const groupSubmitting = ref(false)
const groupFormRef = ref()
const groupFormData = reactive({
  id: null,
  name: '',
  description: '',
  sort_order: 0
})

const groupFormRules = {
  name: [
    { required: true, message: '请输入任务组名称', trigger: 'blur' },
    { min: 1, max: 100, message: '名称长度在 1 到 100 个字符', trigger: 'blur' }
  ]
}

// 监控任务对话框
const taskDialogVisible = ref(false)
const isEditingTask = ref(false)
const taskSubmitting = ref(false)
const taskFormRef = ref()
const taskFormData = reactive({
  id: null,
  name: '',
  description: '',
  task_group_id: null,
  data_source_id: null,
  target_products: [],
  monitor_fields: [],
  frequency_type: 'interval',
  frequency_value: 30,
  cron_expression: '',
  auto_start: false,
  notify_on_error: true
})

const taskFormRules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 1, max: 100, message: '名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  data_source_id: [
    { required: true, message: '请选择数据源', trigger: 'change' }
  ],
  frequency_type: [
    { required: true, message: '请选择频率类型', trigger: 'change' }
  ],
  frequency_value: [
    { required: true, message: '请输入执行间隔', trigger: 'blur', type: 'number' }
  ],
  cron_expression: [
    { required: true, message: '请输入Cron表达式', trigger: 'blur' }
  ]
}

// 计算属性
const selectedGroupName = computed(() => {
  if (selectedGroupId.value === null) {
    return '全部任务'
  }
  const group = taskGroups.value.find(g => g.id === selectedGroupId.value)
  return group ? group.name : '未知任务组'
})

const groupDialogTitle = computed(() => {
  return isEditingGroup.value ? '编辑任务组' : '新建任务组'
})

const taskDialogTitle = computed(() => {
  return isEditingTask.value ? '编辑监控任务' : '新建监控任务'
})

// 目标产品和监控字段的文本表示
const targetProductsText = computed({
  get: () => {
    return Array.isArray(taskFormData.target_products) 
      ? taskFormData.target_products.join('\n') 
      : ''
  },
  set: (value) => {
    taskFormData.target_products = value
      .split(/[\n,]/)
      .map(item => item.trim())
      .filter(item => item)
  }
})

const monitorFieldsText = computed({
  get: () => {
    return Array.isArray(taskFormData.monitor_fields) 
      ? taskFormData.monitor_fields.join('\n') 
      : ''
  },
  set: (value) => {
    taskFormData.monitor_fields = value
      .split(/[\n,]/)
      .map(item => item.trim())
      .filter(item => item)
  }
})

// 方法
async function loadTaskGroups() {
  try {
    groupsLoading.value = true
    const response = await authStore.request('/api/task-groups')
    
    if (response.success) {
      taskGroups.value = response.data.data || []
    } else {
      ElMessage.error(response.message || '加载任务组失败')
    }
  } catch (error) {
    ElMessage.error('加载任务组失败')
  } finally {
    groupsLoading.value = false
  }
}

async function loadMonitoringTasks() {
  try {
    tasksLoading.value = true
    const params = new URLSearchParams({
      page: taskPagination.current_page.toString(),
      per_page: taskPagination.per_page.toString()
    })
    
    if (searchForm.search) {
      params.append('search', searchForm.search)
    }
    if (searchForm.status) {
      params.append('status', searchForm.status)
    }
    if (selectedGroupId.value) {
      params.append('task_group_id', selectedGroupId.value.toString())
    }
    
    const response = await authStore.request(`/api/monitoring-tasks?${params}`)
    
    if (response.success) {
      monitoringTasks.value = response.data.data || []
      taskPagination.current_page = response.data.current_page || 1
      taskPagination.per_page = response.data.per_page || 20
      taskPagination.total = response.data.total || 0
    } else {
      ElMessage.error(response.message || '加载监控任务失败')
    }
  } catch (error) {
    ElMessage.error('加载监控任务失败')
  } finally {
    tasksLoading.value = false
  }
}

async function loadDataSources() {
  try {
    const response = await authStore.request('/api/data-sources?per_page=1000')
    
    if (response.success) {
      dataSources.value = response.data.data || []
    } else {
      ElMessage.error(response.message || '加载数据源失败')
    }
  } catch (error) {
    ElMessage.error('加载数据源失败')
  }
}

async function loadTaskStatuses() {
  try {
    const response = await authStore.request('/api/monitoring-tasks/statuses')
    
    if (response.success) {
      taskStatuses.value = response.data || {}
    }
  } catch (error) {
    console.error('加载任务状态失败:', error)
  }
}

async function loadFrequencyTypes() {
  try {
    const response = await authStore.request('/api/monitoring-tasks/frequency-types')
    
    if (response.success) {
      frequencyTypes.value = response.data || {}
    }
  } catch (error) {
    console.error('加载频率类型失败:', error)
  }
}

async function refreshData() {
  loading.value = true
  try {
    await Promise.all([
      loadTaskGroups(),
      loadMonitoringTasks(),
      loadDataSources(),
      loadTaskStatuses(),
      loadFrequencyTypes()
    ])
  } finally {
    loading.value = false
  }
}

// 任务组相关方法
function selectGroup(groupId) {
  selectedGroupId.value = groupId
  searchForm.task_group_id = groupId
  taskPagination.current_page = 1
  loadMonitoringTasks()
}

function showCreateGroupDialog() {
  isEditingGroup.value = false
  resetGroupForm()
  groupDialogVisible.value = true
}

function showEditGroupDialog(group) {
  isEditingGroup.value = true
  groupFormData.id = group.id
  groupFormData.name = group.name
  groupFormData.description = group.description || ''
  groupFormData.sort_order = group.sort_order || 0
  groupDialogVisible.value = true
}

function resetGroupForm() {
  groupFormData.id = null
  groupFormData.name = ''
  groupFormData.description = ''
  groupFormData.sort_order = 0
}

async function submitGroupForm() {
  try {
    await groupFormRef.value.validate()
    groupSubmitting.value = true
    
    const data = {
      name: groupFormData.name,
      description: groupFormData.description,
      sort_order: groupFormData.sort_order
    }
    
    let response
    if (isEditingGroup.value) {
      response = await authStore.request(`/api/task-groups/${groupFormData.id}`, {
        method: 'PUT',
        body: JSON.stringify(data)
      })
    } else {
      response = await authStore.request('/api/task-groups', {
        method: 'POST',
        body: JSON.stringify(data)
      })
    }
    
    if (response.success) {
      ElMessage.success(isEditingGroup.value ? '任务组更新成功' : '任务组创建成功')
      groupDialogVisible.value = false
      loadTaskGroups()
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    groupSubmitting.value = false
  }
}

async function deleteTaskGroup(group) {
  try {
    await ElMessageBox.confirm(
      `确定要删除任务组 "${group.name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await authStore.request(`/api/task-groups/${group.id}`, {
      method: 'DELETE'
    })
    
    if (response.success) {
      ElMessage.success('任务组删除成功')
      if (selectedGroupId.value === group.id) {
        selectedGroupId.value = null
      }
      loadTaskGroups()
      loadMonitoringTasks()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

function handleGroupAction(command, group) {
  switch (command) {
    case 'edit':
      showEditGroupDialog(group)
      break
    case 'delete':
      deleteTaskGroup(group)
      break
  }
}

// 监控任务相关方法
function showCreateTaskDialog() {
  isEditingTask.value = false
  resetTaskForm()
  taskDialogVisible.value = true
}

function showEditTaskDialog(task) {
  isEditingTask.value = true
  taskFormData.id = task.id
  taskFormData.name = task.name
  taskFormData.description = task.description || ''
  taskFormData.task_group_id = task.task_group_id
  taskFormData.data_source_id = task.data_source_id
  taskFormData.target_products = task.target_products || []
  taskFormData.monitor_fields = task.monitor_fields || []
  taskFormData.frequency_type = task.frequency_type
  taskFormData.frequency_value = task.frequency_value
  taskFormData.cron_expression = task.cron_expression || ''
  taskFormData.auto_start = task.auto_start || false
  taskFormData.notify_on_error = task.notify_on_error !== false
  taskDialogVisible.value = true
}

function resetTaskForm() {
  taskFormData.id = null
  taskFormData.name = ''
  taskFormData.description = ''
  taskFormData.task_group_id = selectedGroupId.value
  taskFormData.data_source_id = null
  taskFormData.target_products = []
  taskFormData.monitor_fields = []
  taskFormData.frequency_type = 'interval'
  taskFormData.frequency_value = 30
  taskFormData.cron_expression = ''
  taskFormData.auto_start = false
  taskFormData.notify_on_error = true
}

async function submitTaskForm() {
  try {
    await taskFormRef.value.validate()
    taskSubmitting.value = true
    
    const data = {
      name: taskFormData.name,
      description: taskFormData.description,
      task_group_id: taskFormData.task_group_id,
      data_source_id: taskFormData.data_source_id,
      target_products: taskFormData.target_products,
      monitor_fields: taskFormData.monitor_fields,
      frequency_type: taskFormData.frequency_type,
      frequency_value: taskFormData.frequency_value,
      cron_expression: taskFormData.cron_expression,
      auto_start: taskFormData.auto_start,
      notify_on_error: taskFormData.notify_on_error
    }
    
    let response
    if (isEditingTask.value) {
      response = await authStore.request(`/api/monitoring-tasks/${taskFormData.id}`, {
        method: 'PUT',
        body: JSON.stringify(data)
      })
    } else {
      response = await authStore.request('/api/monitoring-tasks', {
        method: 'POST',
        body: JSON.stringify(data)
      })
    }
    
    if (response.success) {
      ElMessage.success(isEditingTask.value ? '监控任务更新成功' : '监控任务创建成功')
      taskDialogVisible.value = false
      loadMonitoringTasks()
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    taskSubmitting.value = false
  }
}

function editTask(task) {
  showEditTaskDialog(task)
}

function viewTask(task) {
  ElMessage.info('任务详情功能开发中...')
}

async function deleteTask(task) {
  try {
    await ElMessageBox.confirm(
      `确定要删除监控任务 "${task.name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await authStore.request(`/api/monitoring-tasks/${task.id}`, {
      method: 'DELETE'
    })
    
    if (response.success) {
      ElMessage.success('监控任务删除成功')
      loadMonitoringTasks()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

async function updateTaskStatus(task, status) {
  try {
    const response = await authStore.request(`/api/monitoring-tasks/${task.id}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status })
    })
    
    if (response.success) {
      ElMessage.success('任务状态更新成功')
      loadMonitoringTasks()
    } else {
      ElMessage.error(response.message || '状态更新失败')
    }
  } catch (error) {
    ElMessage.error('状态更新失败')
  }
}

async function duplicateTask(task) {
  try {
    const response = await authStore.request(`/api/monitoring-tasks/${task.id}/duplicate`, {
      method: 'POST'
    })
    
    if (response.success) {
      ElMessage.success('任务复制成功')
      loadMonitoringTasks()
    } else {
      ElMessage.error(response.message || '复制失败')
    }
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

function handleTaskAction(command, task) {
  switch (command) {
    case 'start':
      updateTaskStatus(task, 'running')
      break
    case 'pause':
      updateTaskStatus(task, 'paused')
      break
    case 'stop':
      updateTaskStatus(task, 'stopped')
      break
    case 'duplicate':
      duplicateTask(task)
      break
    case 'delete':
      deleteTask(task)
      break
  }
}

// 事件处理方法
function handleSearch() {
  taskPagination.current_page = 1
  loadMonitoringTasks()
}

function handleTaskSelectionChange(selection) {
  selectedTasks.value = selection
}

function handleTaskPageSizeChange() {
  taskPagination.current_page = 1
  loadMonitoringTasks()
}

function handleTaskCurrentChange() {
  loadMonitoringTasks()
}

function handleDataSourceChange() {
  // 可以在这里根据数据源类型调整表单
}

function handleFrequencyTypeChange() {
  // 清空相关字段
  if (taskFormData.frequency_type === 'interval') {
    taskFormData.cron_expression = ''
  } else {
    taskFormData.frequency_value = null
  }
}

// 工具方法
function getFrequencyTypeLabel(type) {
  return frequencyTypes.value[type] || type
}

function getFrequencyDetail(task) {
  if (task.frequency_type === 'interval') {
    return `每 ${task.frequency_value} 分钟`
  } else if (task.frequency_type === 'cron') {
    return task.cron_expression
  }
  return '-'
}

function getStatusTagType(status) {
  const typeMap = {
    'pending': 'info',
    'running': 'success',
    'paused': 'warning',
    'stopped': 'danger',
    'failed': 'danger'
  }
  return typeMap[status] || 'info'
}

function formatDateTime(dateTime) {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.task-management {
  padding: 24px;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  flex-shrink: 0;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-description {
  color: #606266;
  margin: 0;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

.task-content {
  flex: 1;
  display: flex;
  gap: 24px;
  overflow: hidden;
  min-height: 0;
}

.task-groups {
  width: 320px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.task-list {
  flex: 1;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-width: 0;
}

.section-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.task-filters {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.group-list {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
}

.group-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  margin-bottom: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.group-item:hover {
  background: #f5f7fa;
  border-color: #e4e7ed;
  transform: translateY(-1px);
}

.group-item.active {
  background: #ecf5ff;
  border-color: #409eff;
  color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.group-info {
  flex: 1;
  min-width: 0;
}

.group-name {
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.group-desc {
  font-size: 13px;
  color: #909399;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.group-stats {
  display: flex;
  gap: 8px;
}

.group-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.group-item:hover .group-actions {
  opacity: 1;
}

.tasks-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}

.tasks-table .el-table {
  flex: 1;
}

.task-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.text-muted {
  color: #909399;
}

.pagination {
  padding: 20px 24px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #e4e7ed;
  flex-shrink: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .task-content {
    gap: 20px;
  }
  
  .task-groups {
    width: 300px;
  }
}

@media (max-width: 1200px) {
  .task-management {
    padding: 20px;
  }
  
  .task-content {
    flex-direction: column;
    gap: 20px;
  }
  
  .task-groups {
    width: 100%;
    max-height: 280px;
    flex-shrink: 0;
  }
  
  .group-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    padding: 16px;
  }
  
  .group-item {
    flex: 0 0 calc(33.333% - 12px);
    margin-bottom: 0;
    min-width: 220px;
  }
  
  .task-list {
    flex: 1;
    min-height: 500px;
  }
}

@media (max-width: 992px) {
  .task-management {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .group-item {
    flex: 0 0 calc(50% - 12px);
    min-width: 180px;
  }
  
  .task-filters {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .task-filters .el-input {
    width: 200px !important;
    min-width: 150px;
  }
  
  .task-filters .el-select {
    width: 140px !important;
    min-width: 120px;
  }
}

@media (max-width: 768px) {
  .task-management {
    padding: 12px;
    height: 100vh;
  }
  
  .page-header {
    margin-bottom: 16px;
  }
  
  .page-title {
    font-size: 22px;
  }
  
  .page-description {
    font-size: 14px;
  }
  
  .header-actions {
    gap: 8px;
  }
  
  .header-actions .el-button {
    font-size: 13px;
    padding: 8px 12px;
  }
  
  .task-groups {
    max-height: 200px;
  }
  
  .group-list {
    gap: 8px;
    padding: 12px;
  }
  
  .group-item {
    flex: 0 0 100%;
    min-width: 0;
    padding: 12px;
  }
  
  .group-name {
    font-size: 15px;
  }
  
  .group-desc {
    font-size: 12px;
  }
  
  .task-filters {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .task-filters .el-input,
  .task-filters .el-select {
    width: 100% !important;
  }
  
  .task-filters .el-button {
    width: 100%;
    justify-content: center;
  }
  
  .section-header {
    padding: 16px 20px;
  }
  
  .section-header h3 {
    font-size: 16px;
  }
  
  /* 表格在移动端的优化 */
  .tasks-table :deep(.el-table) {
    font-size: 13px;
  }
  
  .tasks-table :deep(.el-table th),
  .tasks-table :deep(.el-table td) {
    padding: 8px 6px;
  }
  
  .tasks-table :deep(.el-table__cell) {
    padding: 8px 6px;
  }
  
  /* 隐藏部分列在移动端 */
  .tasks-table :deep(.el-table__column--selection) {
    display: none;
  }
  
  .tasks-table :deep(.el-table th:nth-child(4)),
  .tasks-table :deep(.el-table td:nth-child(4)),
  .tasks-table :deep(.el-table th:nth-child(6)),
  .tasks-table :deep(.el-table td:nth-child(6)),
  .tasks-table :deep(.el-table th:nth-child(7)),
  .tasks-table :deep(.el-table td:nth-child(7)) {
    display: none;
  }
  
  .pagination {
    padding: 16px 12px;
    justify-content: center;
  }
  
  .pagination :deep(.el-pagination) {
    font-size: 12px;
  }
  
  .pagination :deep(.el-pagination .el-select .el-input) {
    width: 80px;
  }
}

@media (max-width: 480px) {
  .task-management {
    padding: 8px;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .page-description {
    font-size: 13px;
  }
  
  .header-actions .el-button {
    font-size: 12px;
    padding: 6px 8px;
  }
  
  .section-header {
    padding: 12px 16px;
  }
  
  .section-header h3 {
    font-size: 15px;
  }
  
  .group-item {
    padding: 8px;
  }
  
  .group-name {
    font-size: 14px;
  }
  
  .group-desc {
    font-size: 11px;
  }
  
  .tasks-table :deep(.el-table) {
    font-size: 12px;
  }
  
  .tasks-table :deep(.el-table th),
  .tasks-table :deep(.el-table td) {
    padding: 6px 4px;
  }
  
  /* 进一步简化表格显示 */
  .tasks-table :deep(.el-table th:nth-child(3)),
  .tasks-table :deep(.el-table td:nth-child(3)),
  .tasks-table :deep(.el-table th:nth-child(5)),
  .tasks-table :deep(.el-table td:nth-child(5)),
  .tasks-table :deep(.el-table th:nth-child(8)),
  .tasks-table :deep(.el-table td:nth-child(8)) {
    display: none;
  }
  
  .pagination {
    padding: 12px 8px;
  }
  
  .pagination :deep(.el-pagination) {
    font-size: 11px;
  }
  
  /* 简化分页组件 */
  .pagination :deep(.el-pagination .btn-prev),
  .pagination :deep(.el-pagination .btn-next) {
    padding: 0 4px;
  }
  
  .pagination :deep(.el-pagination .el-pager li) {
    min-width: 24px;
    height: 24px;
    line-height: 24px;
    font-size: 11px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 360px) {
  .task-management {
    padding: 4px;
  }
  
  .page-title {
    font-size: 18px;
  }
  
  .page-description {
    display: none;
  }
  
  .header-actions .el-button span {
    display: none;
  }
  
  .header-actions .el-button {
    padding: 4px 6px;
    min-width: 32px;
  }
  
  .section-header h3 {
    font-size: 14px;
  }
  
  .tasks-table :deep(.el-table) {
    font-size: 11px;
  }
  
  /* 只显示最核心的列 */
  .tasks-table :deep(.el-table th:not(:nth-child(2)):not(:nth-child(4)):not(:last-child)),
  .tasks-table :deep(.el-table td:not(:nth-child(2)):not(:nth-child(4)):not(:last-child)) {
    display: none;
  }
}

/* 加载状态 */
.el-loading-mask {
  border-radius: 12px;
}

/* 表格样式调整 */
.el-table .el-table__cell {
  padding: 12px 8px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}
</style> 