{"models": {"main": {"provider": "openrouter", "modelId": "google/gemini-2.5-pro-preview-05-06", "maxTokens": 120000, "temperature": 0.2}, "research": {"provider": "openrouter", "modelId": "google/gemini-2.5-pro-preview-05-06", "maxTokens": 8700, "temperature": 0.1}, "fallback": {"provider": "openrouter", "modelId": "google/gemini-2.5-pro-preview-05-06", "maxTokens": 8192, "temperature": 0.1}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "defaultTag": "master", "azureOpenaiBaseURL": "https://your-endpoint.openai.azure.com/", "userId": "**********"}}