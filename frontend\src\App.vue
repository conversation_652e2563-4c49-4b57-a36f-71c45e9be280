<script setup lang="ts">
import { onMounted } from 'vue'
import { useAuthStore } from './stores/auth'

const authStore = useAuthStore()

// 应用初始化时检查认证状态
onMounted(async () => {
  await authStore.initAuth()
})
</script>

<template>
  <div id="app">
    <router-view />
  </div>
</template>

<style>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  width: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

#app {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
}

/* Element Plus 样式调整 */
.el-container {
  height: 100%;
  width: 100%;
}

.el-header {
  display: flex;
  align-items: center;
  padding: 0;
  height: 60px;
}

.el-aside {
  overflow: hidden;
  height: 100%;
}

.el-main {
  padding: 0;
  height: 100%;
  flex: 1;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 响应式基础样式 */
@media (max-width: 768px) {
  html, body {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  html, body {
    font-size: 13px;
  }
}

/* 确保所有容器都是满屏 */
.el-container,
.app-layout,
.el-header,
.el-aside,
.el-main {
  max-width: none !important;
  width: 100% !important;
}
</style>
