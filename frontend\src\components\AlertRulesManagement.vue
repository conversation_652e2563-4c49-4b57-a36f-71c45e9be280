<template>
  <div class="alert-rules-management">
    <!-- 工具栏 -->
    <div class="toolbar mb-4 flex justify-between items-center">
      <el-button type="primary" :icon="Plus" @click="handleCreate">新建规则</el-button>
      <div>
        <el-input v-model="searchKeyword" placeholder="搜索规则名称" class="w-64 mr-2" clearable @clear="fetchRules" @keyup.enter="fetchRules" />
        <el-button :icon="Search" @click="fetchRules" :loading="loading">搜索</el-button>
      </div>
    </div>

    <!-- 规则表格 -->
    <el-table :data="rules" v-loading="loading" style="width: 100%">
      <el-table-column prop="name" label="规则名称" width="200" />
      <el-table-column prop="product_info" label="监控产品" width="200">
        <template #default="{ row }">
          {{ row.product_target_type }}: {{ row.product_target_value }}
        </template>
      </el-table-column>
      <el-table-column prop="conditions" label="触发条件">
         <template #default="{ row }">
          <div v-for="(condition, index) in row.conditions" :key="index">
            <el-tag type="info" size="small">{{ condition.field }} {{ condition.operator }} {{ condition.value }}</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="is_active" label="状态" width="100">
        <template #default="{ row }">
          <el-switch v-model="row.is_active" @change="handleStatusChange(row)" />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="{ row }">
          <el-button size="small" type="primary" :icon="Edit" @click="handleEdit(row)">编辑</el-button>
          <el-button size="small" type="danger" :icon="Delete" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container mt-4 flex justify-end">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 编辑/新建对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="60%">
      <el-form :model="currentRule" ref="ruleForm" label-width="120px" :rules="formRules">
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="currentRule.name" />
        </el-form-item>
        <el-form-item label="监控产品类型" prop="product_target_type">
            <el-select v-model="currentRule.product_target_type" placeholder="请选择">
                <el-option label="SKU" value="sku"></el-option>
                <el-option label="SPU" value="spu"></el-option>
                <el-option label="类目" value="category"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="监控产品值" prop="product_target_value">
            <el-input v-model="currentRule.product_target_value" placeholder="请输入SKU/SPU/类目ID"/>
        </el-form-item>
        
        <el-divider>触发条件</el-divider>
        <div v-for="(condition, index) in currentRule.conditions" :key="index" class="condition-item mb-2">
            <el-row :gutter="10">
                <el-col :span="6">
                    <el-select v-model="condition.field" placeholder="字段">
                        <el-option v-for="item in ruleOptions.fields" :key="item.value" :label="item.label" :value="item.value"/>
                    </el-select>
                </el-col>
                <el-col :span="6">
                     <el-select v-model="condition.operator" placeholder="操作符">
                        <el-option v-for="item in ruleOptions.operators" :key="item.value" :label="item.label" :value="item.value"/>
                    </el-select>
                </el-col>
                <el-col :span="6">
                    <el-input v-model="condition.value" placeholder="值" />
                </el-col>
                <el-col :span="4">
                    <el-button type="danger" :icon="Delete" @click="removeCondition(index)"></el-button>
                </el-col>
            </el-row>
        </div>
        <el-button type="success" plain @click="addCondition">添加条件</el-button>

        <el-divider>通知设置</el-divider>
        <el-form-item label="通知渠道" prop="notification_channels">
             <el-checkbox-group v-model="currentRule.notification_channels">
                <el-checkbox v-for="item in ruleOptions.channels" :key="item.value" :label="item.value">{{ item.label }}</el-checkbox>
            </el-checkbox-group>
        </el-form-item>
         <el-form-item label="冷却时间(分钟)" prop="cooldown_period">
            <el-input-number v-model="currentRule.cooldown_period" :min="0" />
        </el-form-item>

      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Search, Edit, Delete } from '@element-plus/icons-vue';
import api from '../api/alerts'; // 假设的API模块

const loading = ref(false);
const rules = ref([]);
const searchKeyword = ref('');
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

const dialogVisible = ref(false);
const dialogTitle = ref('');
const currentRule = ref({});
const ruleForm = ref(null);
const ruleOptions = ref({ fields: [], operators: [], channels: [] });

const formRules = {
  name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  // 其他规则
};

const fetchRules = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.currentPage,
      per_page: pagination.pageSize,
      search: searchKeyword.value,
    };
    const response = await api.getAlertRules(params);
    rules.value = response.data;
    pagination.total = response.total;
  } catch (error) {
    ElMessage.error('获取预警规则失败');
  } finally {
    loading.value = false;
  }
};

const fetchRuleOptions = async () => {
    try {
        const response = await api.getAlertRuleOptions();
        ruleOptions.value = response.data;
    } catch (error) {
        ElMessage.error('获取规则选项失败');
    }
}

const handleCreate = () => {
  currentRule.value = {
    name: '',
    product_target_type: 'sku',
    product_target_value: '',
    conditions: [{ field: 'price', operator: '>', value: '' }],
    notification_channels: ['database'],
    is_active: true,
    cooldown_period: 60,
  };
  dialogTitle.value = '新建预警规则';
  dialogVisible.value = true;
};

const handleEdit = (row) => {
  currentRule.value = JSON.parse(JSON.stringify(row));
  dialogTitle.value = '编辑预警规则';
  dialogVisible.value = true;
};

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要删除规则 "${row.name}" 吗?`, '提示', {
      type: 'warning',
    });
    await api.deleteAlertRule(row.id);
    ElMessage.success('删除成功');
    fetchRules();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};

const handleStatusChange = async (row) => {
  try {
    const action = row.is_active ? api.activateAlertRule : api.deactivateAlertRule;
    await action(row.id);
    ElMessage.success('状态更新成功');
  } catch (error) {
    ElMessage.error('状态更新失败');
    row.is_active = !row.is_active; // 恢复原状
  }
};

const submitForm = async () => {
  await ruleForm.value.validate(async (valid) => {
    if (valid) {
      try {
        if (currentRule.value.id) {
          await api.updateAlertRule(currentRule.value.id, currentRule.value);
          ElMessage.success('更新成功');
        } else {
          await api.createAlertRule(currentRule.value);
          ElMessage.success('创建成功');
        }
        dialogVisible.value = false;
        fetchRules();
      } catch (error) {
        ElMessage.error('操作失败');
      }
    }
  });
};

const addCondition = () => {
    if (!currentRule.value.conditions) {
        currentRule.value.conditions = [];
    }
    currentRule.value.conditions.push({ field: 'price', operator: '>', value: '' });
}

const removeCondition = (index) => {
    currentRule.value.conditions.splice(index, 1);
}

const handleSizeChange = (val) => {
  pagination.pageSize = val;
  fetchRules();
};

const handleCurrentChange = (val) => {
  pagination.currentPage = val;
  fetchRules();
};

onMounted(() => {
  fetchRules();
  fetchRuleOptions();
});
</script>

<style scoped>
.condition-item {
    padding: 10px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
}
</style> 