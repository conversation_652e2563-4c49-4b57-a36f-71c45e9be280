<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 数据源功能测试 ===\n";

try {
    // 1. 测试创建数据源
    echo "\n1. 测试创建数据源...\n";
    $dataSource = new App\Models\DataSource();
    $dataSource->name = '测试数据源';
    $dataSource->type = 'api';
    $dataSource->description = '这是一个测试数据源';
    $dataSource->api_base_url = 'https://api.example.com';
    $dataSource->owner_id = 1; // 管理员用户
    $dataSource->api_config = json_encode([
        'api_key' => 'test-key',
        'timeout' => 30
    ]);
    $dataSource->status = 1;
    $dataSource->save();
    
    echo "✓ 数据源创建成功，ID: " . $dataSource->id . "\n";
    
    // 2. 测试获取数据源
    echo "\n2. 测试获取数据源...\n";
    $foundDataSource = App\Models\DataSource::find($dataSource->id);
    if ($foundDataSource) {
        echo "✓ 数据源获取成功: " . $foundDataSource->name . "\n";
        echo "  类型: " . $foundDataSource->type . "\n";
        echo "  状态: " . ($foundDataSource->status ? '启用' : '禁用') . "\n";
        echo "  配置: " . json_encode($foundDataSource->api_config, JSON_UNESCAPED_UNICODE) . "\n";
    } else {
        echo "✗ 数据源获取失败\n";
    }
    
    // 3. 测试数据源类型
    echo "\n3. 测试数据源类型...\n";
    $types = App\Models\DataSource::getAvailableTypes();
    echo "✓ 可用数据源类型: " . implode(', ', $types) . "\n";
    
    // 4. 测试按类型获取
    echo "\n4. 测试按类型获取...\n";
    $apiSources = App\Models\DataSource::getByType('api');
    echo "  API类型数据源: " . $apiSources->count() . " 个\n";
    
    // 5. 测试API URL构建
    echo "\n5. 测试API URL构建...\n";
    $url = $foundDataSource->getFullApiUrl('/products');
    echo "✓ API URL: " . $url . "\n";
    
    // 6. 测试更新统计
    echo "\n6. 测试更新统计...\n";
    $foundDataSource->updateUsageStats(true);
    $foundDataSource->refresh();
    echo "✓ 统计更新成功，总请求数: " . $foundDataSource->total_requests . "\n";
    
    // 7. 测试删除数据源
    echo "\n7. 测试删除数据源...\n";
    $foundDataSource->delete();
    echo "✓ 数据源删除成功\n";
    
    echo "\n=== 所有测试通过 ===\n";
    
} catch (Exception $e) {
    echo "✗ 测试失败: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
} 