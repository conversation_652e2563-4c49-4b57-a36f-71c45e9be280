<template>
  <div class="about">
    <el-card>
      <template #header>
        <h1>关于系统</h1>
      </template>
      
      <el-descriptions title="系统信息" :column="1" border>
        <el-descriptions-item label="系统名称">
          电商市场动态监测系统
        </el-descriptions-item>
        <el-descriptions-item label="版本">
          v1.0.0
        </el-descriptions-item>
        <el-descriptions-item label="技术栈">
          Vue.js 3 + TypeScript + Element Plus + Laravel
        </el-descriptions-item>
      </el-descriptions>
      
      <el-divider />
      
      <p>电商市场动态监测系统是一个用于监控电商平台商品价格和竞品动态的专业工具。</p>
      
      <h3>主要功能</h3>
      <el-timeline>
        <el-timeline-item timestamp="功能模块" placement="top">
          <el-card>
            <h4>渠道价格监测</h4>
            <p>实时监控各个电商渠道的商品价格变化</p>
          </el-card>
        </el-timeline-item>
        <el-timeline-item timestamp="功能模块" placement="top">
          <el-card>
            <h4>竞品动态监控</h4>
            <p>跟踪竞争对手的产品动态和市场策略</p>
          </el-card>
        </el-timeline-item>
        <el-timeline-item timestamp="功能模块" placement="top">
          <el-card>
            <h4>预警规则设置</h4>
            <p>自定义预警规则，及时获得重要信息通知</p>
          </el-card>
        </el-timeline-item>
        <el-timeline-item timestamp="功能模块" placement="top">
          <el-card>
            <h4>数据分析报告</h4>
            <p>生成详细的数据分析报告和可视化图表</p>
          </el-card>
        </el-timeline-item>
      </el-timeline>
      
      <el-divider />
      
      <el-button type="primary">
        <router-link to="/" style="color: white; text-decoration: none;">
          返回首页
        </router-link>
      </el-button>
    </el-card>
  </div>
</template>

<script setup lang="ts">
// About页面逻辑
</script>

<style scoped>
.about {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

h1 {
  color: #2c3e50;
  margin: 0;
}

h3 {
  color: #409eff;
  margin: 20px 0 10px 0;
}

h4 {
  color: #2c3e50;
  margin: 0 0 10px 0;
}

p {
  color: #666;
  line-height: 1.6;
  margin: 10px 0;
}
</style> 