<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FieldMapping extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'data_source_id',
        'source_field',
        'target_field',
        'field_type',
        'transform_rule',
        'default_value',
        'is_required',
        'validation_rule',
        'description',
        'sort_order',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'transform_rule' => 'array',
        'validation_rule' => 'array',
        'is_required' => 'boolean',
        'status' => 'integer',
        'sort_order' => 'integer',
    ];

    /**
     * 所属数据源
     */
    public function dataSource(): BelongsTo
    {
        return $this->belongsTo(DataSource::class);
    }

    /**
     * 检查字段映射是否启用
     */
    public function isEnabled(): bool
    {
        return $this->status === 1;
    }

    /**
     * 应用转换规则
     */
    public function transformValue($value)
    {
        // 如果值为空且有默认值，使用默认值
        if (empty($value) && !empty($this->default_value)) {
            $value = $this->default_value;
        }

        // 根据字段类型转换
        switch ($this->field_type) {
            case 'number':
                return is_numeric($value) ? (float) $value : 0;
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'date':
                return $value ? date('Y-m-d H:i:s', strtotime($value)) : null;
            case 'json':
                return is_string($value) ? json_decode($value, true) : $value;
            default:
                return (string) $value;
        }
    }

    /**
     * 验证字段值
     */
    public function validateValue($value): array
    {
        $errors = [];

        // 检查必填
        if ($this->is_required && empty($value)) {
            $errors[] = "字段 {$this->target_field} 不能为空";
        }

        // 应用验证规则
        if (!empty($this->validation_rule) && is_array($this->validation_rule)) {
            foreach ($this->validation_rule as $rule => $ruleValue) {
                switch ($rule) {
                    case 'min_length':
                        if (strlen($value) < $ruleValue) {
                            $errors[] = "字段 {$this->target_field} 长度不能少于 {$ruleValue} 个字符";
                        }
                        break;
                    case 'max_length':
                        if (strlen($value) > $ruleValue) {
                            $errors[] = "字段 {$this->target_field} 长度不能超过 {$ruleValue} 个字符";
                        }
                        break;
                    case 'pattern':
                        if (!preg_match($ruleValue, $value)) {
                            $errors[] = "字段 {$this->target_field} 格式不正确";
                        }
                        break;
                }
            }
        }

        return $errors;
    }

    /**
     * 按数据源获取字段映射
     */
    public static function getByDataSource(int $dataSourceId): \Illuminate\Database\Eloquent\Collection
    {
        return static::where('data_source_id', $dataSourceId)
            ->where('status', 1)
            ->orderBy('sort_order')
            ->orderBy('target_field')
            ->get();
    }

    /**
     * 获取可用的字段类型
     */
    public static function getAvailableFieldTypes(): array
    {
        return [
            'string' => '字符串',
            'number' => '数字',
            'boolean' => '布尔值',
            'date' => '日期时间',
            'json' => 'JSON对象',
        ];
    }
} 