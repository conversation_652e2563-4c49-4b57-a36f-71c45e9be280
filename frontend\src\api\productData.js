import apiClient from './apiClient';

const getProductData = (params) => {
  return apiClient.get('/product-data', { params });
};

const getProductDetail = (id) => {
  return apiClient.get(`/product-data/${id}`);
};

const getPriceHistory = (id, params) => {
  return apiClient.get(`/product-data/${id}/price-history`, { params });
};

const compareProducts = (data) => {
  return apiClient.post('/product-data/compare', data);
};

export default {
  getProductData,
  getProductDetail,
  getPriceHistory,
  compareProducts,
}; 