<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 清空表以避免外键约束问题
        DB::table('product_data')->truncate();

        Schema::table('product_data', function (Blueprint $table) {
            // 确保 data_source_id 列存在才进行操作
            if (Schema::hasColumn('product_data', 'data_source_id')) {
                // 检查并删除外键
                $foreignKeys = DB::select("SELECT CONSTRAINT_NAME FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? AND COLUMN_NAME = ? AND REFERENCED_TABLE_NAME = 'data_sources'", [DB::getDatabaseName(), 'product_data', 'data_source_id']);
                if (!empty($foreignKeys)) {
                    $table->dropForeign('product_data_data_source_id_foreign');
                }

                // 检查并删除唯一索引
                $indexes = DB::select("SHOW INDEX FROM product_data WHERE Key_name = 'product_data_data_source_id_item_id_unique'");
                if (!empty($indexes)) {
                    $table->dropUnique('product_data_data_source_id_item_id_unique');
                }
                
                // 删除列
                $table->dropColumn('data_source_id');
            }

            // 添加新列和约束
            if (!Schema::hasColumn('product_data', 'monitoring_task_id')) {
                $table->foreignId('monitoring_task_id')->after('id')->constrained('monitoring_tasks')->onDelete('cascade')->comment('监控任务ID');
            }
            if (!Schema::hasColumn('product_data', 'price')) {
                $table->decimal('price', 10, 2)->after('standardized_data')->nullable()->comment('从standardized_data提取的价格');
            }
            
            $indexes = DB::select("SHOW INDEX FROM product_data WHERE Key_name = 'product_data_monitoring_task_id_item_id_unique'");
            if (empty($indexes)) {
                 $table->unique(['monitoring_task_id', 'item_id']);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // down 方法逻辑暂时简化，因为正向迁移是关键
        Schema::table('product_data', function (Blueprint $table) {
            if (Schema::hasColumn('product_data', 'monitoring_task_id')) {
                 $table->dropForeign(['monitoring_task_id']);
                 $table->dropUnique('product_data_monitoring_task_id_item_id_unique');
                 $table->dropColumn('monitoring_task_id');
            }
            if (Schema::hasColumn('product_data', 'price')) {
                 $table->dropColumn('price');
            }
            if (!Schema::hasColumn('product_data', 'data_source_id')) {
                $table->foreignId('data_source_id')->after('id')->constrained('data_sources')->onDelete('cascade');
                $table->unique(['data_source_id', 'item_id']);
            }
        });
    }
};
