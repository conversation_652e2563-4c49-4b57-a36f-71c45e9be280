<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AuditLog extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'audit_logs';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'user_name',
        'action',
        'model_type',
        'model_id',
        'table_name',
        'old_values',
        'new_values',
        'details',
        'ip_address',
        'user_agent',
        'url',
        'method',
        'level',
        'description',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'details' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 日志级别常量
     */
    const LEVEL_INFO = 'info';
    const LEVEL_WARNING = 'warning';
    const LEVEL_ERROR = 'error';
    const LEVEL_CRITICAL = 'critical';

    /**
     * 操作类型常量
     */
    const ACTION_CREATE = 'create';
    const ACTION_UPDATE = 'update';
    const ACTION_DELETE = 'delete';
    const ACTION_LOGIN = 'login';
    const ACTION_LOGOUT = 'logout';
    const ACTION_VIEW = 'view';
    const ACTION_EXPORT = 'export';
    const ACTION_IMPORT = 'import';
    const ACTION_ACTIVATE = 'activate';
    const ACTION_DEACTIVATE = 'deactivate';

    /**
     * 获取操作用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取所有可用的日志级别
     *
     * @return array
     */
    public static function getLevels(): array
    {
        return [
            self::LEVEL_INFO => '信息',
            self::LEVEL_WARNING => '警告',
            self::LEVEL_ERROR => '错误',
            self::LEVEL_CRITICAL => '严重',
        ];
    }

    /**
     * 获取所有可用的操作类型
     *
     * @return array
     */
    public static function getActions(): array
    {
        return [
            self::ACTION_CREATE => '创建',
            self::ACTION_UPDATE => '更新',
            self::ACTION_DELETE => '删除',
            self::ACTION_LOGIN => '登录',
            self::ACTION_LOGOUT => '登出',
            self::ACTION_VIEW => '查看',
            self::ACTION_EXPORT => '导出',
            self::ACTION_IMPORT => '导入',
            self::ACTION_ACTIVATE => '激活',
            self::ACTION_DEACTIVATE => '停用',
        ];
    }

    /**
     * 获取日志级别的中文名称
     *
     * @return string
     */
    public function getLevelNameAttribute(): string
    {
        return self::getLevels()[$this->level] ?? $this->level;
    }

    /**
     * 获取操作类型的中文名称
     *
     * @return string
     */
    public function getActionNameAttribute(): string
    {
        return self::getActions()[$this->action] ?? $this->action;
    }

    /**
     * 获取模型名称的中文描述
     *
     * @return string
     */
    public function getModelNameAttribute(): string
    {
        $modelNames = [
            'App\\Models\\User' => '用户',
            'App\\Models\\Role' => '角色',
            'App\\Models\\Permission' => '权限',
            'App\\Models\\DataSource' => '数据源',
            'App\\Models\\MonitoringTask' => '监控任务',
            'App\\Models\\TaskGroup' => '任务组',
            'App\\Models\\AlertRule' => '预警规则',
            'App\\Models\\Alert' => '预警',
            'App\\Models\\ProductData' => '产品数据',
        ];

        return $modelNames[$this->model_type] ?? $this->model_type;
    }

    /**
     * 范围查询：按用户过滤
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 范围查询：按操作类型过滤
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $action
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByAction($query, string $action)
    {
        return $query->where('action', $action);
    }

    /**
     * 范围查询：按模型类型过滤
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $modelType
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByModelType($query, string $modelType)
    {
        return $query->where('model_type', $modelType);
    }

    /**
     * 范围查询：按日志级别过滤
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $level
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByLevel($query, string $level)
    {
        return $query->where('level', $level);
    }

    /**
     * 范围查询：按时间范围过滤
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $startDate
     * @param string $endDate
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByDateRange($query, string $startDate, string $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * 范围查询：最近的日志
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRecent($query)
    {
        return $query->orderBy('created_at', 'desc');
    }
}