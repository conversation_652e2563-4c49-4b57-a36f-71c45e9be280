<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;

class CheckPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$permissions): Response
    {
        // 检查用户是否已认证
        if (!$request->user()) {
            return response()->json([
                'success' => false,
                'message' => '未认证，请先登录'
            ], 401);
        }

        // 如果没有指定权限，只需要认证即可
        if (empty($permissions)) {
            return $next($request);
        }

        $user = $request->user();
        
        // 检查用户状态
        if ($user->status !== 1) {
            return response()->json([
                'success' => false,
                'message' => '账户已被禁用'
            ], 403);
        }
        
        // 加载用户的角色和权限（使用缓存优化）
        $user->load('roles.permissions');
        
        // 记录权限检查日志
        Log::info('权限检查', [
            'user_id' => $user->id,
            'username' => $user->username,
            'required_permissions' => $permissions,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'method' => $request->method()
        ]);
        
        // 检查是否满足权限要求（支持多权限检查）
        $hasPermission = $this->checkPermissions($user, $permissions);
        
        if (!$hasPermission) {
            Log::warning('权限验证失败', [
                'user_id' => $user->id,
                'username' => $user->username,
                'required_permissions' => $permissions,
                'user_permissions' => $user->getAllPermissions()->pluck('name')->toArray(),
                'ip' => $request->ip(),
                'url' => $request->fullUrl()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '权限不足，无法访问此资源',
                'required_permissions' => $permissions
            ], 403);
        }

        return $next($request);
    }
    
    /**
     * 检查用户权限
     * 
     * @param \App\Models\User $user
     * @param array $permissions
     * @return bool
     */
    private function checkPermissions($user, array $permissions): bool
    {
        // 如果是超级管理员，拥有所有权限
        if ($user->hasRole('super_admin')) {
            return true;
        }
        
        // 检查是否拥有任一权限（OR逻辑）
        foreach ($permissions as $permission) {
            // 支持权限组合检查，使用 & 分隔表示AND逻辑
            if (str_contains($permission, '&')) {
                $requiredPermissions = explode('&', $permission);
                $hasAllPermissions = true;
                
                foreach ($requiredPermissions as $requiredPermission) {
                    if (!$user->hasPermission(trim($requiredPermission))) {
                        $hasAllPermissions = false;
                        break;
                    }
                }
                
                if ($hasAllPermissions) {
                    return true;
                }
            } else {
                // 单个权限检查
                if ($user->hasPermission($permission)) {
                    return true;
                }
            }
        }
        
        return false;
    }
}
