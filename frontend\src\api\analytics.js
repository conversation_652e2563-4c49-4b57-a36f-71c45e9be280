import apiClient from './apiClient';

export default {
  /**
   * 获取促销分析数据
   * @param {object} params - 过滤参数 (start_date, end_date, category_id, keyword)
   */
  getPromotionAnalysis(params) {
    return apiClient.get('/analytics/promotion-analysis', { params });
  },

  /**
   * 获取产品价格趋势图表数据
   * @param {object} body - 过滤参数 (start_date, end_date, item_ids, category_id, keyword)
   */
  getPriceTrendChart(body) {
    return apiClient.post('/analytics/price-trend-chart', body);
  },

  /**
   * 计算产品核心指标
   * @param {object} body - 过滤参数 (item_ids, date, compare_date)
   */
  calculateProductMetrics(body) {
    return apiClient.post('/analytics/product-metrics', body);
  },

  /**
   * 获取总体促销强度指数
   * @param {object} params - 过滤参数 (start_date, end_date, category_id)
   */
  getPromotionIntensityIndex(params) {
    return apiClient.get('/analytics/promotion-intensity', { params });
  },

  /**
   * 获取类目价格带分布
   * @param {object} params - 过滤参数 (category_id, date)
   */
  getCategoryPriceDistribution(params) {
    return apiClient.get('/analytics/category-price-distribution', { params });
  },

  /**
   * 获取综合分析报告
   * @param {object} body - 过滤参数 (start_date, end_date, category_id, keyword)
   */
  getComprehensiveAnalysisReport(body) {
    return apiClient.post('/analytics/comprehensive-report', body);
  },

  /**
   * 清除分析缓存
   */
  clearCache() {
    return apiClient.delete('/analytics/cache');
  },

  /**
   * 获取仪表盘统计数据
   */
  getDashboardStatistics() {
    return apiClient.get('/analytics/statistics');
  },
  
  /**
   * (假设) 获取产品类目列表
   */
  getCategories() {
      // 这是一个模拟的API调用, 实际项目中需要后端提供相应的接口
      // return apiClient.get('/categories');
      return Promise.resolve({
          data: [
              { id: 1, name: '手机数码' },
              { id: 2, name: '家用电器' },
              { id: 3, name: '电脑办公' },
              { id: 4, name: '服饰鞋包' },
          ]
      })
  }
}; 