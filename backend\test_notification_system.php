<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

// 创建Laravel应用实例
$app = Application::configure(basePath: __DIR__)
    ->withRouting(
        web: __DIR__.'/routes/web.php',
        api: __DIR__.'/routes/api.php',
        commands: __DIR__.'/routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();

$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Models\DataSource;
use App\Models\MonitoringTask;
use App\Models\AlertRule;
use App\Models\ProductData;
use App\Models\Alert;
use App\Services\NotificationService;
use Illuminate\Support\Facades\DB;

echo "开始测试通知系统...\n\n";

try {
    // 1. 创建测试用户
    echo "1. 创建测试用户...\n";
    $user = User::firstOrCreate(
        ['email' => '<EMAIL>'],
        [
            'name' => '测试用户',
            'username' => 'testuser',
            'password' => bcrypt('password'),
        ]
    );
    echo "用户ID: {$user->id}, 邮箱: {$user->email}\n\n";

    // 2. 创建测试数据源
    echo "2. 创建测试数据源...\n";
    $dataSource = DataSource::firstOrCreate(
        ['name' => '测试电商平台'],
        [
            'type' => 'api',
            'api_base_url' => 'https://api.test.com',
            'api_endpoint_template' => '/products/{item_id}',
            'api_method' => 'GET',
            'timeout' => 30,
            'status' => 'active',
        ]
    );
    echo "数据源ID: {$dataSource->id}\n\n";

    // 3. 创建监控任务
    echo "3. 创建监控任务...\n";
    $task = MonitoringTask::firstOrCreate(
        [
            'name' => '通知测试任务',
            'user_id' => $user->id,
            'data_source_id' => $dataSource->id,
        ],
        [
            'description' => '测试通知功能',
            'target_products' => ['NOTIFY001'],
            'frequency_type' => 'minutes',
            'frequency_value' => 30,
            'status' => 'active',
        ]
    );
    echo "监控任务ID: {$task->id}\n\n";

    // 4. 创建配置了通知的预警规则
    echo "4. 创建配置了通知的预警规则...\n";
    $alertRule = AlertRule::firstOrCreate(
        [
            'name' => '价格低于50元预警（含通知）',
            'monitoring_task_id' => $task->id,
        ],
        [
            'description' => '当产品价格低于50元时触发预警并发送通知',
            'user_id' => $user->id,
            'rule_type' => 'price_decrease',
            'target_field' => 'price',
            'operator' => '<',
            'threshold_values' => [50],
            'severity' => 'high',
            'priority' => 1,
            'notification_channels' => ['email', 'database'], // 配置通知渠道
            'recipients' => [$user->id], // 接收者为测试用户
            'cooldown_minutes' => 5,
            'max_alerts_per_hour' => 10,
            'max_alerts_per_day' => 50,
            'status' => 1, // 启用
        ]
    );
    echo "预警规则ID: {$alertRule->id}\n";
    echo "通知渠道: " . implode(', ', $alertRule->notification_channels) . "\n";
    echo "接收者: " . implode(', ', $alertRule->recipients) . "\n\n";

    // 5. 创建测试产品数据（价格为45，应该触发预警）
    echo "5. 创建测试产品数据（价格45元，应该触发预警）...\n";
    $productData = ProductData::create([
        'data_source_id' => $dataSource->id,
        'item_id' => 'NOTIFY001',
        'raw_data' => [
            'id' => 'NOTIFY001',
            'name' => '通知测试商品',
            'price' => 45.00,
            'stock' => 8,
            'status' => '在售',
        ],
        'standardized_data' => [
            'title' => '通知测试商品',
            'price' => 45.00,
            'quantity' => 8,
            'status' => '在售',
        ],
        'last_collected_at' => now(),
    ]);
    echo "产品数据ID: {$productData->id}\n\n";

    // 6. 手动创建一个预警记录用于测试通知
    echo "6. 创建预警记录...\n";
    $alert = Alert::create([
        'monitoring_task_id' => $task->id,
        'alert_rule_id' => $alertRule->id,
        'product_id' => $productData->id,
        'alert_type' => 'price_decrease',
        'alert_level' => 'high',
        'title' => $alertRule->name,
        'message' => '产品 "通知测试商品" 的 price 当前值为 45，触发了预警条件 < 50',
        'trigger_data' => [
            'target_field' => 'price',
            'target_value' => 45.00,
            'operator' => '<',
            'threshold_values' => [50],
            'product_data' => [
                'item_id' => $productData->item_id,
                'data_source' => $dataSource->name,
                'standardized_data' => $productData->standardized_data,
            ],
        ],
        'status' => 0, // 未处理
    ]);
    echo "预警ID: {$alert->id}\n\n";

    // 7. 测试通知服务
    echo "7. 测试通知发送...\n";
    $notificationService = new NotificationService();
    $result = $notificationService->sendAlertNotification($alert);
    
    echo "通知发送结果:\n";
    echo "- 成功: " . ($result['success'] ? '是' : '否') . "\n";
    echo "- 接收者数量: " . ($result['recipients_count'] ?? 0) . "\n";
    echo "- 发送成功数量: " . ($result['sent_count'] ?? 0) . "\n";
    echo "- 发送失败数量: " . ($result['failed_count'] ?? 0) . "\n";
    
    if (isset($result['errors']) && !empty($result['errors'])) {
        echo "- 错误信息:\n";
        foreach ($result['errors'] as $error) {
            echo "  * 接收者ID {$error['recipient_id']}: {$error['error']}\n";
        }
    }
    echo "\n";

    // 8. 检查数据库通知记录
    echo "8. 检查数据库通知记录...\n";
    $notifications = DB::table('notifications')
        ->where('notifiable_id', $user->id)
        ->where('notifiable_type', 'App\\Models\\User')
        ->orderBy('created_at', 'desc')
        ->get();

    if ($notifications->count() > 0) {
        echo "共找到 {$notifications->count()} 条通知记录:\n";
        foreach ($notifications as $notification) {
            $data = json_decode($notification->data, true);
            echo "- 通知ID: {$notification->id}\n";
            echo "  类型: {$notification->type}\n";
            echo "  标题: " . ($data['title'] ?? '无标题') . "\n";
            echo "  消息: " . ($data['message'] ?? '无消息') . "\n";
            echo "  级别: " . ($data['alert_level'] ?? '未知') . "\n";
            echo "  状态: " . ($notification->read_at ? '已读' : '未读') . "\n";
            echo "  创建时间: {$notification->created_at}\n";
            echo "\n";
        }
    } else {
        echo "没有找到通知记录。\n\n";
    }

    // 9. 测试发送测试通知
    echo "9. 发送测试通知...\n";
    $testResult = $notificationService->sendTestNotification($user, '这是一条通知系统测试消息');
    
    echo "测试通知结果:\n";
    echo "- 成功: " . ($testResult['success'] ? '是' : '否') . "\n";
    if (!$testResult['success']) {
        echo "- 错误: " . ($testResult['error'] ?? '未知错误') . "\n";
    }
    echo "\n";

    // 10. 再次检查通知记录
    echo "10. 再次检查通知记录（包含测试通知）...\n";
    $allNotifications = DB::table('notifications')
        ->where('notifiable_id', $user->id)
        ->where('notifiable_type', 'App\\Models\\User')
        ->orderBy('created_at', 'desc')
        ->get();

    echo "总共有 {$allNotifications->count()} 条通知记录\n\n";

    echo "通知系统测试完成！\n";
    echo "\n注意事项:\n";
    echo "1. 邮件发送需要在 .env 文件中配置 MAIL_* 相关参数\n";
    echo "2. 如果邮件发送失败，请检查邮件服务器配置\n";
    echo "3. 数据库通知已成功保存，可以在前端界面中显示\n";

} catch (Exception $e) {
    echo "测试过程中发生错误: " . $e->getMessage() . "\n";
    echo "错误追踪: " . $e->getTraceAsString() . "\n";
} 