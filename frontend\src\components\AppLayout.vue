<template>
  <el-container class="app-layout">
    <!-- 顶部导航栏 -->
    <el-header class="app-header">
      <div class="header-left">
        <el-button class="sidebar-toggle" @click="toggleSidebar" text>
          <el-icon><Menu /></el-icon>
        </el-button>
        <div class="app-title-section">
          <h1 class="app-title">智能电商市场动态监测系统</h1>
          <p class="app-subtitle">E-commerce Market Intelligence Platform</p>
        </div>
      </div>
      <div class="header-right">
        <el-badge :value="unreadNotifications" class="notification-badge" type="danger" :hidden="unreadNotifications === 0">
          <el-button :icon="Bell" circle class="notification-btn" @click="handleNotificationClick" />
        </el-badge>
        <el-dropdown @command="handleUserAction" class="user-dropdown">
          <div class="user-info">
            <el-avatar :size="36" :src="authStore.user?.avatar" class="user-avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
            <div class="user-details">
              <span class="user-name">{{ authStore.user?.name || authStore.user?.username }}</span>
              <span class="user-role">{{ authStore.isAdmin ? '系统管理员' : '普通用户' }}</span>
            </div>
            <el-icon class="dropdown-arrow"><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="messages">
                <el-icon><Message /></el-icon>
                我的消息
                <el-badge v-if="unreadMessages > 0" :value="unreadMessages" class="message-badge" />
              </el-dropdown-item>
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>
                个人资料
              </el-dropdown-item>
              <el-dropdown-item command="password">
                <el-icon><Lock /></el-icon>
                修改密码
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <!-- 主体内容 -->
    <el-container class="main-container">
      <!-- 移动端遮罩层 -->
      <div v-if="isMobile && showMobileSidebar" class="mobile-overlay show" @click="closeMobileSidebar"></div>
      
      <!-- 侧边栏 -->
      <el-aside 
        :width="isMobile ? '250px' : sidebarWidth" 
        :class="['app-sidebar', { 'mobile-show': isMobile && showMobileSidebar }]"
      >
        <el-scrollbar class="sidebar-scrollbar">
          <el-menu
            :default-active="activeMenu"
            :collapse="!isMobile && isCollapsed"
            :unique-opened="false"
            router
            class="sidebar-menu"
          >
            <!-- 业务监控 -->
            <el-sub-menu index="business-monitoring" class="main-menu-item">
              <template #title>
                <el-icon><Monitor /></el-icon>
                <span>业务监控</span>
              </template>
              
              <!-- 渠道价格监测 -->
              <el-sub-menu index="channel-price" class="sub-menu-item">
                <template #title>
                  <el-icon><TrendCharts /></el-icon>
                  <span>渠道价格监测</span>
                </template>
                              <el-menu-item index="/channel-price/tasks" class="menu-leaf" @click="closeMobileSidebar">
                <el-icon><Setting /></el-icon>
                <template #title>任务管理</template>
              </el-menu-item>
                <el-menu-item index="/channel-price/dashboard" class="menu-leaf" @click="closeMobileSidebar">
                  <el-icon><DataBoard /></el-icon>
                  <template #title>数据看板</template>
                </el-menu-item>
                <el-menu-item index="/channel-price/alerts" class="menu-leaf" @click="closeMobileSidebar">
                  <el-icon><Bell /></el-icon>
                  <template #title>预警中心</template>
                </el-menu-item>
              </el-sub-menu>
              
              <!-- 竞品动态监测 -->
              <el-sub-menu index="competitor-monitoring" class="sub-menu-item">
                <template #title>
                  <el-icon><View /></el-icon>
                  <span>竞品动态监测</span>
                </template>
                <el-menu-item index="/competitor/tasks" class="menu-leaf" @click="closeMobileSidebar">
                  <el-icon><Setting /></el-icon>
                  <template #title>任务管理</template>
                </el-menu-item>
                <el-menu-item index="/competitor/dashboard" class="menu-leaf" @click="closeMobileSidebar">
                  <el-icon><DataAnalysis /></el-icon>
                  <template #title>数据看板</template>
                </el-menu-item>
                <el-menu-item index="/competitor/alerts" class="menu-leaf" @click="closeMobileSidebar">
                  <el-icon><Bell /></el-icon>
                  <template #title>预警中心</template>
                </el-menu-item>
              </el-sub-menu>
              
              <!-- 相似同款查询 -->
              <el-sub-menu index="similar-search" class="sub-menu-item">
                <template #title>
                  <el-icon><Search /></el-icon>
                  <span>相似同款查询</span>
                </template>
                <el-menu-item index="/similar/live-search" class="menu-leaf" @click="closeMobileSidebar">
                  <el-icon><Lightning /></el-icon>
                  <template #title>实时搜索</template>
                </el-menu-item>
                <el-menu-item index="/similar/monitoring" class="menu-leaf" @click="closeMobileSidebar">
                  <el-icon><Timer /></el-icon>
                  <template #title>监控任务</template>
                </el-menu-item>
                <el-menu-item index="/similar/dashboard" class="menu-leaf" @click="closeMobileSidebar">
                  <el-icon><Histogram /></el-icon>
                  <template #title>数据看板</template>
                </el-menu-item>
              </el-sub-menu>
            </el-sub-menu>

            <!-- 系统管理 - 仅管理员可见 -->
            <el-sub-menu index="system-management" v-if="authStore.isAdmin" class="main-menu-item">
              <template #title>
                <el-icon><Tools /></el-icon>
                <span>系统管理</span>
              </template>
              <el-menu-item index="/system/data-sources" class="menu-leaf" @click="closeMobileSidebar">
                <el-icon><Connection /></el-icon>
                <template #title>数据源管理</template>
              </el-menu-item>
              <el-menu-item index="/system/users" class="menu-leaf" @click="closeMobileSidebar">
                <el-icon><UserFilled /></el-icon>
                <template #title>用户管理</template>
              </el-menu-item>
              <el-menu-item index="/system/roles" class="menu-leaf" @click="closeMobileSidebar">
                <el-icon><Key /></el-icon>
                <template #title>角色与权限</template>
              </el-menu-item>
              <el-menu-item index="/system/settings" class="menu-leaf" @click="closeMobileSidebar">
                <el-icon><Setting /></el-icon>
                <template #title>系统设置</template>
              </el-menu-item>
              <el-menu-item index="/system/audit-logs" class="menu-leaf" @click="closeMobileSidebar">
                <el-icon><DocumentCopy /></el-icon>
                <template #title>操作日志</template>
              </el-menu-item>
            </el-sub-menu>

            <!-- 个人中心 -->
            <el-sub-menu index="profile" class="main-menu-item">
              <template #title>
                <el-icon><User /></el-icon>
                <span>个人中心</span>
              </template>
              <el-menu-item index="/profile/messages" class="menu-leaf" @click="closeMobileSidebar">
                <el-icon><Message /></el-icon>
                <template #title>我的消息</template>
                <el-badge v-if="unreadMessages > 0" :value="unreadMessages" class="menu-badge" />
              </el-menu-item>
              <el-menu-item index="/profile/password" class="menu-leaf" @click="closeMobileSidebar">
                <el-icon><Lock /></el-icon>
                <template #title>修改密码</template>
              </el-menu-item>
            </el-sub-menu>
          </el-menu>
        </el-scrollbar>
      </el-aside>

      <!-- 主内容区域 -->
      <el-main :class="['app-main', { 'collapsed': !isMobile && isCollapsed }]">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  User,
  ArrowDown,
  Message,
  Lock,
  SwitchButton,
  Bell,
  Monitor,
  TrendCharts,
  Setting,
  DataBoard,
  View,
  DataAnalysis,
  Search,
  Lightning,
  Timer,
  Histogram,
  Tools,
  Connection,
  UserFilled,
  Key,
  DocumentCopy,
  Menu
} from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'

const authStore = useAuthStore()
const route = useRoute()
const router = useRouter()

// 侧边栏状态
const isCollapsed = ref(false)
const isMobile = ref(false)
const showMobileSidebar = ref(false)
const sidebarWidth = computed(() => isCollapsed.value ? '64px' : '250px')

// 通知相关
const unreadNotifications = ref(5)
const unreadMessages = ref(3)

// 当前激活的菜单项
const activeMenu = computed(() => {
  return route.path
})

// 检测是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
  if (!isMobile.value) {
    showMobileSidebar.value = false
  }
}

// 切换侧边栏
const toggleSidebar = () => {
  if (isMobile.value) {
    showMobileSidebar.value = !showMobileSidebar.value
  } else {
    isCollapsed.value = !isCollapsed.value
  }
}

// 关闭移动端侧边栏
const closeMobileSidebar = () => {
  if (isMobile.value) {
    showMobileSidebar.value = false
  }
}

// 处理通知点击
const handleNotificationClick = () => {
  router.push('/profile/messages')
}

// 处理用户下拉菜单
const handleUserAction = async (command: string) => {
  switch (command) {
    case 'messages':
      router.push('/profile/messages')
      break
    case 'profile':
      ElMessage.info('个人资料功能开发中...')
      break
    case 'password':
      router.push('/profile/password')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        authStore.logout()
        router.push('/login')
        ElMessage.success('已成功退出登录')
      } catch {
        // 用户取消
      }
      break
  }
}

// 监听路由变化，更新页面标题
watch(route, (to) => {
  const routeTitle = to.meta?.title as string
  if (routeTitle) {
    document.title = `${routeTitle} - 智能电商市场动态监测系统`
  }
}, { immediate: true })

onMounted(() => {
  // 检查用户权限
  if (!authStore.isLoggedIn) {
    router.push('/login')
  }
  
  // 初始检测移动端
  checkMobile()
  
  // 监听窗口大小变化
  window.addEventListener('resize', checkMobile)
})

// 清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<style scoped>
/* 全局布局 */
.app-layout {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.main-container {
  height: calc(100vh - 60px);
  overflow: hidden;
}

/* 头部 */
.app-header {
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
  min-width: 0;
  flex: 1;
}

.sidebar-toggle {
  color: white;
  font-size: 18px;
  border-radius: 8px;
  transition: all 0.3s ease;
  padding: 8px;
  min-width: 40px;
  height: 40px;
  flex-shrink: 0;
}

.sidebar-toggle:hover {
  background-color: rgba(255, 255, 255, 0.15);
  transform: scale(1.05);
}

.app-title-section {
  display: flex;
  flex-direction: column;
  min-width: 0;
  flex: 1;
}

.app-title {
  font-size: 22px;
  font-weight: 700;
  margin: 0;
  line-height: 1.2;
  background: linear-gradient(45deg, #fff, #e8f4ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.app-subtitle {
  font-size: 12px;
  opacity: 0.85;
  margin-top: 2px;
  font-style: italic;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
}

.notification-badge {
  position: relative;
}

.notification-btn {
  color: white;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.user-dropdown {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  min-width: 0;
}

.user-info:hover {
  background-color: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

.user-avatar {
  border: 2px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 0;
  flex: 1;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.user-role {
  font-size: 12px;
  opacity: 0.8;
  margin-top: 2px;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.dropdown-arrow {
  font-size: 12px;
  opacity: 0.8;
  transition: transform 0.3s ease;
  flex-shrink: 0;
}

.user-info:hover .dropdown-arrow {
  transform: rotate(180deg);
}

/* 侧边栏 */
.app-sidebar {
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
  transition: width 0.3s ease, transform 0.3s ease;
  overflow: hidden;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  height: 100%;
}

.sidebar-scrollbar {
  height: 100%;
}

.sidebar-menu {
  border: none;
  background: transparent;
  height: 100%;
}

.main-menu-item {
  margin: 6px 12px;
  border-radius: 8px;
  overflow: hidden;
}

.main-menu-item :deep(.el-sub-menu__title) {
  color: #ecf0f1;
  font-weight: 600;
  padding-left: 20px !important;
  height: 52px;
  line-height: 52px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.main-menu-item :deep(.el-sub-menu__title:hover) {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  transform: translateX(4px);
}

.sub-menu-item {
  margin: 4px 0;
}

.sub-menu-item :deep(.el-sub-menu__title) {
  color: #bdc3c7;
  padding-left: 40px !important;
  height: 44px;
  line-height: 44px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.sub-menu-item :deep(.el-sub-menu__title:hover) {
  background-color: rgba(52, 152, 219, 0.2);
  color: #3498db;
  transform: translateX(2px);
}

.menu-leaf {
  color: #95a5a6;
  padding-left: 60px !important;
  height: 40px;
  line-height: 40px;
  margin: 2px 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.menu-leaf:hover {
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(52, 152, 219, 0.2));
  color: #3498db;
  transform: translateX(2px);
}

.menu-leaf.is-active {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.menu-badge {
  margin-left: 8px;
}

/* 主内容区 */
.app-main {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 0;
  overflow: auto;
  flex: 1;
  height: 100%;
  min-width: 0; /* 防止flex子项溢出 */
}

.main-content {
  min-height: 100%;
  height: 100%;
  padding: 0;
  width: 100%;
  box-sizing: border-box;
}

/* PC端布局确保 */
@media (min-width: 769px) {
  .main-container {
    display: flex;
    flex-direction: row;
  }

  .app-sidebar {
    position: relative;
    transform: none;
    flex-shrink: 0;
  }

  .app-main {
    flex: 1;
    width: calc(100% - 250px);
    margin-left: 0;
    overflow-y: auto;
  }

  .app-main.collapsed {
    width: calc(100% - 64px);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-right {
    gap: 12px;
  }

  .user-name,
  .user-role {
    max-width: 100px;
  }
}

@media (max-width: 992px) {
  .app-header {
    padding: 0 12px;
  }

  .header-left {
    gap: 12px;
  }

  .app-title {
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .app-header {
    padding: 0 8px;
  }
  
  .header-left {
    gap: 8px;
  }
  
  .app-title {
    font-size: 18px;
  }
  
  .app-subtitle {
    display: none;
  }
  
  .user-details {
    display: none;
  }
  
  .user-info {
    padding: 8px;
    gap: 0;
  }
  
  .header-right {
    gap: 8px;
  }
  
  .notification-btn {
    width: 36px;
    height: 36px;
  }
  
  .user-avatar {
    width: 32px;
    height: 32px;
  }
  
  /* 移动端侧边栏优化 */
  .app-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1001; /* 比遮罩层高 */
    height: 100vh;
    transform: translateX(-100%);
  }
  
  .app-sidebar.mobile-show {
    transform: translateX(0);
  }
  
  .app-main {
    margin-left: 0 !important;
    width: 100% !important;
    padding: 0 !important;
    overflow-y: auto !important;
  }
  
  /* 移动端菜单项优化 */
  .sidebar-menu :deep(.el-menu-item),
  .sidebar-menu :deep(.el-sub-menu__title) {
    height: 48px !important;
    line-height: 48px !important;
    font-size: 15px !important;
    padding-left: 20px !important;
  }
  
  .sidebar-menu :deep(.el-sub-menu .el-menu-item) {
    padding-left: 40px !important;
  }
  
  .sidebar-menu :deep(.el-sub-menu .el-sub-menu .el-menu-item) {
    padding-left: 60px !important;
  }
  
  .sidebar-menu :deep(.el-icon) {
    margin-right: 12px !important;
    width: 20px !important;
    height: 20px !important;
    font-size: 20px !important;
  }

  .mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }
  
  .mobile-overlay.show {
    opacity: 1;
    visibility: visible;
  }
}

@media (max-width: 480px) {
  .app-header {
    padding: 0 4px;
  }
  
  .app-title {
    font-size: 16px;
  }
  
  .header-left {
    gap: 4px;
  }
  
  .header-right {
    gap: 4px;
  }
  
  .sidebar-toggle,
  .notification-btn {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
  
  .user-avatar {
    width: 28px;
    height: 28px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 360px) {
  .app-title {
    font-size: 14px;
  }
}

/* 暗色主题适配 */
.dark .app-header {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.dark .app-sidebar {
  background: linear-gradient(180deg, #1a1a1a 0%, #2d2d2d 100%);
}

.dark .app-main {
  background: linear-gradient(135deg, #121212 0%, #1e1e1e 100%);
}

/* 滚动条样式 */
:deep(.el-scrollbar__bar) {
  opacity: 0.4;
  transition: opacity 0.3s ease;
}

:deep(.el-scrollbar__bar:hover) {
  opacity: 0.8;
}

:deep(.el-scrollbar__thumb) {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

/* 菜单图标样式 */
:deep(.el-menu-item .el-icon),
:deep(.el-sub-menu__title .el-icon) {
  margin-right: 10px;
  width: 18px;
  height: 18px;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

:deep(.el-menu-item:hover .el-icon),
:deep(.el-sub-menu__title:hover .el-icon) {
  transform: scale(1.1);
}

/* 消息徽章样式 */
.message-badge {
  margin-left: auto;
}

/* 动画效果 */
.sidebar-menu {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 下拉菜单样式优化 */
:deep(.el-dropdown-menu) {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
}

:deep(.el-dropdown-menu .el-dropdown-menu__item) {
  padding: 12px 20px;
  transition: all 0.3s ease;
}

:deep(.el-dropdown-menu .el-dropdown-menu__item:hover) {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  transform: translateX(2px);
}

/* 确保Element Plus组件满屏 */
:deep(.el-container) {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
}

:deep(.el-header) {
  width: 100% !important;
  height: 60px !important;
  flex-shrink: 0 !important;
}

:deep(.el-aside) {
  height: 100% !important;
  flex-shrink: 0 !important;
}

:deep(.el-main) {
  flex: 1 !important;
  height: 100% !important;
  padding: 0 !important;
  overflow: auto !important;
  min-width: 0 !important;
}

/* 移动端强制样式 */
@media (max-width: 768px) {
  :deep(.el-main) {
    width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
    overflow-x: hidden !important;
    overflow-y: auto !important;
  }
  
  /* 确保路由内容正确显示 */
  .app-main > * {
    width: 100% !important;
    min-height: 100% !important;
  }
}


</style> 