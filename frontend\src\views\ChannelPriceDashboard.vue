<template>
  <div class="channel-price-dashboard">
    <!-- 页面标题和统计信息 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">渠道价格监测看板</h1>
        <p class="page-description">监控各渠道的产品价格变化，进行跨平台价格对比分析</p>
      </div>
      <div class="header-stats">
        <div class="stat-card" v-loading="statsLoading">
          <div class="stat-number">{{ statistics.total_products || 0 }}</div>
          <div class="stat-label">监控产品</div>
        </div>
        <div class="stat-card" v-loading="statsLoading">
          <div class="stat-number">{{ statistics.recent_products || 0 }}</div>
          <div class="stat-label">本周更新</div>
        </div>
        <div class="stat-card" v-loading="statsLoading">
          <div class="stat-number">{{ dataSources.length }}</div>
          <div class="stat-label">数据源</div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索栏 -->
    <div class="filter-bar">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索产品名称或ID"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.task_id"
            placeholder="选择监控任务"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="task in monitoringTasks"
              :key="task.id"
              :label="task.name"
              :value="task.id"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.data_source_id"
            placeholder="选择数据源"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="source in dataSources"
              :key="source.id"
              :label="source.name"
              :value="source.id"
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="handleDateRangeChange"
          />
        </el-col>
        <el-col :span="4">
          <el-button @click="resetFilters" :icon="RefreshLeft">重置</el-button>
          <el-button 
            type="primary" 
            @click="showCompareDialog" 
            :disabled="selectedProducts.length < 2"
            :icon="TrendCharts"
          >
            对比 ({{ selectedProducts.length }})
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 产品数据表格 -->
    <div class="data-table">
      <el-table
        v-loading="loading"
        :data="productData"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="image" label="图片" width="80">
          <template #default="{ row }">
            <el-image
              v-if="row.image"
              :src="row.image"
              :preview-src-list="[row.image]"
              fit="cover"
              style="width: 50px; height: 50px; border-radius: 4px;"
              :preview-aria-label="row.title"
            />
            <div v-else class="no-image">
              <el-icon><Picture /></el-icon>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="产品名称" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="product-title">
              <span>{{ row.title }}</span>
              <div class="product-meta">
                <el-tag size="small" type="info">{{ row.item_id }}</el-tag>
                <el-tag size="small" v-if="row.brand">{{ row.brand }}</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="data_source" label="数据源" width="120">
          <template #default="{ row }">
            <el-tag size="small" type="primary">{{ row.data_source }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="当前价格" width="120" sortable="custom">
          <template #default="{ row }">
            <div class="price-info">
              <span class="current-price">¥{{ formatPrice(row.price) }}</span>
              <span v-if="row.original_price && row.original_price > row.price" class="original-price">
                ¥{{ formatPrice(row.original_price) }}
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="库存" width="80">
          <template #default="{ row }">
            <el-tag :type="getQuantityTagType(row.quantity)" size="small">
              {{ row.quantity || 0 }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="small">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="category" label="分类" width="120" show-overflow-tooltip />
        <el-table-column prop="last_collected_at" label="更新时间" width="160" sortable="custom">
          <template #default="{ row }">
            {{ formatDateTime(row.last_collected_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewProductDetail(row)" :icon="View">
              详情
            </el-button>
            <el-button size="small" @click="viewPriceHistory(row)" :icon="TrendCharts">
              趋势
            </el-button>
            <el-button 
              v-if="row.url" 
              size="small" 
              @click="openProductUrl(row.url)" 
              :icon="Link"
            >
              访问
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.current_page"
          v-model:page-size="pagination.per_page"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 产品对比对话框 -->
    <el-dialog
      v-model="compareDialogVisible"
      title="产品价格对比"
      width="90%"
      :close-on-click-modal="false"
    >
      <div class="compare-content" v-loading="compareLoading">
        <el-table :data="compareData" stripe style="width: 100%">
          <el-table-column prop="image" label="图片" width="80">
            <template #default="{ row }">
              <el-image
                v-if="row.image"
                :src="row.image"
                fit="cover"
                style="width: 50px; height: 50px; border-radius: 4px;"
              />
            </template>
          </el-table-column>
          <el-table-column prop="title" label="产品名称" min-width="200" show-overflow-tooltip />
          <el-table-column prop="data_source" label="数据源" width="120">
            <template #default="{ row }">
              <el-tag size="small" type="primary">{{ row.data_source }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="price" label="价格" width="120">
            <template #default="{ row }">
              <span :class="getPriceCompareClass(row.price)">
                ¥{{ formatPrice(row.price) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="quantity" label="库存" width="80" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)" size="small">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="last_collected_at" label="更新时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.last_collected_at) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <el-button @click="compareDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 产品详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="产品详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="product-detail" v-if="selectedProduct">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-image
              v-if="selectedProduct.image"
              :src="selectedProduct.image"
              fit="cover"
              style="width: 100%; max-height: 300px; border-radius: 8px;"
            />
            <div v-else class="no-image-large">
              <el-icon><Picture /></el-icon>
              <span>暂无图片</span>
            </div>
          </el-col>
          <el-col :span="16">
            <div class="detail-info">
              <h3>{{ selectedProduct.title }}</h3>
              <div class="detail-row">
                <label>商品ID:</label>
                <span>{{ selectedProduct.item_id }}</span>
              </div>
              <div class="detail-row">
                <label>数据源:</label>
                <el-tag type="primary">{{ selectedProduct.data_source }}</el-tag>
              </div>
              <div class="detail-row">
                <label>当前价格:</label>
                <span class="price">¥{{ formatPrice(selectedProduct.price) }}</span>
                <span v-if="selectedProduct.original_price && selectedProduct.original_price > selectedProduct.price" class="original-price">
                  原价: ¥{{ formatPrice(selectedProduct.original_price) }}
                </span>
              </div>
              <div class="detail-row">
                <label>库存:</label>
                <span>{{ selectedProduct.quantity || 0 }}</span>
              </div>
              <div class="detail-row">
                <label>状态:</label>
                <el-tag :type="getStatusTagType(selectedProduct.status)">{{ selectedProduct.status }}</el-tag>
              </div>
              <div class="detail-row" v-if="selectedProduct.brand">
                <label>品牌:</label>
                <span>{{ selectedProduct.brand }}</span>
              </div>
              <div class="detail-row" v-if="selectedProduct.category">
                <label>分类:</label>
                <span>{{ selectedProduct.category }}</span>
              </div>
              <div class="detail-row">
                <label>更新时间:</label>
                <span>{{ formatDateTime(selectedProduct.last_collected_at) }}</span>
              </div>
              <div class="detail-row" v-if="selectedProduct.description">
                <label>描述:</label>
                <p class="description">{{ selectedProduct.description }}</p>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button v-if="selectedProduct?.url" type="primary" @click="openProductUrl(selectedProduct.url)">
          访问商品页面
        </el-button>
      </template>
    </el-dialog>

    <!-- 价格趋势对话框 -->
    <el-dialog
      v-model="priceHistoryDialogVisible"
      title="价格趋势"
      width="900px"
      :close-on-click-modal="false"
    >
      <div class="price-history" v-loading="priceHistoryLoading">
        <div v-if="priceHistoryData.length > 0" ref="priceChart" style="width: 100%; height: 400px;"></div>
        <div v-else class="no-data">
          <el-empty description="暂无价格历史数据" />
        </div>
      </div>
      <template #footer>
        <el-button @click="priceHistoryDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElDialog } from 'element-plus'
import {
  Search,
  RefreshLeft,
  TrendCharts,
  Picture,
  View,
  Link
} from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'
import analyticsApi from '@/api/analytics'
import { getAllDataSources } from '@/api/dataSource'
import { getAllMonitoringTasks } from '@/api/monitoringTask'
import productDataApi from '@/api/productData'

const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const statsLoading = ref(false)
const compareLoading = ref(false)
const priceHistoryLoading = ref(false)

// 数据
const productData = ref([])
const dataSources = ref([])
const monitoringTasks = ref([])
const selectedProducts = ref([])
const statistics = ref({})

// 搜索表单
const searchForm = reactive({
  search: '',
  task_id: null,
  data_source_id: null,
  date_from: '',
  date_to: ''
})

const dateRange = ref([])

// 分页信息
const pagination = reactive({
  current_page: 1,
  per_page: 20,
  total: 0
})

// 排序信息
const sortInfo = reactive({
  sort_by: 'last_collected_at',
  sort_order: 'desc'
})

// 对话框状态
const compareDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const priceHistoryDialogVisible = ref(false)

// 对话框数据
const compareData = ref([])
const selectedProduct = ref(null)
const priceHistoryData = ref([])
const priceChart = ref()

// 方法
async function loadProductData() {
  try {
    loading.value = true
    const params = new URLSearchParams({
      page: pagination.current_page.toString(),
      per_page: pagination.per_page.toString(),
      sort_by: sortInfo.sort_by,
      sort_order: sortInfo.sort_order
    })
    
    if (searchForm.search) {
      params.append('search', searchForm.search)
    }
    if (searchForm.task_id) {
      params.append('task_id', searchForm.task_id.toString())
    }
    if (searchForm.data_source_id) {
      params.append('data_source_id', searchForm.data_source_id.toString())
    }
    if (searchForm.date_from) {
      params.append('date_from', searchForm.date_from)
    }
    if (searchForm.date_to) {
      params.append('date_to', searchForm.date_to)
    }
    
    const response = await productDataApi.getProductData(params)
    
    if (response.success) {
      productData.value = response.data.data || []
      pagination.current_page = response.data.current_page || 1
      pagination.per_page = response.data.per_page || 20
      pagination.total = response.data.meta.total || 0
    } else {
      ElMessage.error(response.message || '加载产品数据失败')
    }
  } catch (error) {
    ElMessage.error('加载产品数据失败')
  } finally {
    loading.value = false
  }
}

async function loadStatistics() {
  try {
    statsLoading.value = true
    const response = await analyticsApi.getDashboardStatistics()
    
    if (response.success) {
      statistics.value = response.data || {}
    }
  } catch (error) {
    console.error('加载统计信息失败:', error)
  } finally {
    statsLoading.value = false
  }
}

async function loadDataSources() {
  try {
    const response = await getAllDataSources()
    
    if (response.success) {
      dataSources.value = response.data.data || []
    }
  } catch (error) {
    console.error('加载数据源失败:', error)
  }
}

async function loadMonitoringTasks() {
  try {
    const response = await getAllMonitoringTasks()
    
    if (response.success) {
      monitoringTasks.value = response.data.data || []
    }
  } catch (error) {
    console.error('加载监控任务失败:', error)
  }
}

async function refreshData() {
  await Promise.all([
    loadProductData(),
    loadStatistics(),
    loadDataSources(),
    loadMonitoringTasks()
  ])
}

// 事件处理方法
function handleSearch() {
  pagination.current_page = 1
  loadProductData()
  loadStatistics()
}

function handleDateRangeChange(value) {
  if (value && value.length === 2) {
    searchForm.date_from = value[0]
    searchForm.date_to = value[1]
  } else {
    searchForm.date_from = ''
    searchForm.date_to = ''
  }
  handleSearch()
}

function resetFilters() {
  searchForm.search = ''
  searchForm.task_id = null
  searchForm.data_source_id = null
  searchForm.date_from = ''
  searchForm.date_to = ''
  dateRange.value = []
  pagination.current_page = 1
  sortInfo.sort_by = 'last_collected_at'
  sortInfo.sort_order = 'desc'
  refreshData()
}

function handleSelectionChange(selection) {
  selectedProducts.value = selection
}

function handleSortChange({ prop, order }) {
  if (prop) {
    sortInfo.sort_by = prop
    sortInfo.sort_order = order === 'ascending' ? 'asc' : 'desc'
    loadProductData()
  }
}

function handlePageSizeChange() {
  pagination.current_page = 1
  loadProductData()
}

function handleCurrentChange() {
  loadProductData()
}

// 产品操作方法
function viewProductDetail(product) {
  selectedProduct.value = product
  detailDialogVisible.value = true
}

async function viewPriceHistory(product) {
  try {
    priceHistoryLoading.value = true
    priceHistoryDialogVisible.value = true
    
    const params = new URLSearchParams({
      item_id: product.item_id,
      data_source_id: product.data_source_id.toString(),
      days: '30'
    })
    
    const response = await authStore.request(`/api/product-data/price-history?${params}`)
    
    if (response.success) {
      priceHistoryData.value = response.data || []
      await nextTick()
      renderPriceChart()
    } else {
      ElMessage.error(response.message || '加载价格历史失败')
    }
  } catch (error) {
    ElMessage.error('加载价格历史失败')
  } finally {
    priceHistoryLoading.value = false
  }
}

function openProductUrl(url) {
  window.open(url, '_blank')
}

async function showCompareDialog() {
  if (selectedProducts.value.length < 2) {
    ElMessage.warning('请至少选择2个产品进行对比')
    return
  }
  
  try {
    compareLoading.value = true
    compareDialogVisible.value = true
    
    const productIds = selectedProducts.value.map(p => p.id)
    const response = await authStore.request('/api/product-data/compare', {
      method: 'POST',
      body: JSON.stringify({ product_ids: productIds })
    })
    
    if (response.success) {
      compareData.value = response.data || []
    } else {
      ElMessage.error(response.message || '加载对比数据失败')
    }
  } catch (error) {
    ElMessage.error('加载对比数据失败')
  } finally {
    compareLoading.value = false
  }
}

// 工具方法
function formatPrice(price) {
  return parseFloat(price || 0).toFixed(2)
}

function formatDateTime(dateTime) {
  return new Date(dateTime).toLocaleString('zh-CN')
}

function getQuantityTagType(quantity) {
  const qty = parseInt(quantity || 0)
  if (qty === 0) return 'danger'
  if (qty < 10) return 'warning'
  return 'success'
}

function getStatusTagType(status) {
  const statusMap = {
    '在售': 'success',
    '有库存': 'success',
    '现货': 'success',
    '缺货': 'danger',
    '下架': 'info',
    '预售': 'warning'
  }
  return statusMap[status] || 'info'
}

function getPriceCompareClass(price) {
  if (!compareData.value.length) return ''
  
  const prices = compareData.value.map(item => parseFloat(item.price || 0))
  const minPrice = Math.min(...prices)
  const maxPrice = Math.max(...prices)
  const currentPrice = parseFloat(price || 0)
  
  if (currentPrice === minPrice) return 'price-lowest'
  if (currentPrice === maxPrice) return 'price-highest'
  return ''
}

function renderPriceChart() {
  // 这里可以集成图表库如 ECharts 来渲染价格趋势图
  // 由于当前项目可能没有安装图表库，我们暂时显示数据表格
  console.log('Price history data:', priceHistoryData.value)
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script> 