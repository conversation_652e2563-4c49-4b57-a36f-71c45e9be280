<?php
/**
 * 监控任务和任务组API测试文件
 * 使用方法：在后端目录下运行 php test_monitoring_api.php
 */

require_once 'vendor/autoload.php';

// 测试配置
$baseUrl = 'http://localhost:8000/api';
$testUser = [
    'username' => 'admin',
    'password' => 'admin123'
];

class MonitoringApiTester
{
    private $baseUrl;
    private $token;
    
    public function __construct($baseUrl)
    {
        $this->baseUrl = $baseUrl;
    }
    
    /**
     * 执行HTTP请求
     */
    private function makeRequest($method, $endpoint, $data = null, $headers = [])
    {
        $url = $this->baseUrl . $endpoint;
        $ch = curl_init();
        
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array_merge([
            'Content-Type: application/json',
            'Accept: application/json',
        ], $headers));
        
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        return [
            'status' => $httpCode,
            'data' => json_decode($response, true)
        ];
    }
    
    /**
     * 用户登录获取token
     */
    public function login($username, $password)
    {
        echo "正在登录用户: $username\n";
        
        $response = $this->makeRequest('POST', '/auth/login', [
            'username' => $username,
            'password' => $password
        ]);
        
        if ($response['status'] === 200 && isset($response['data']['data']['token'])) {
            $this->token = $response['data']['data']['token'];
            echo "✓ 登录成功，获取到token\n";
            return true;
        }
        
        echo "✗ 登录失败: " . ($response['data']['message'] ?? '未知错误') . "\n";
        return false;
    }
    
    /**
     * 获取认证头
     */
    private function getAuthHeaders()
    {
        return ['Authorization: Bearer ' . $this->token];
    }
    
    /**
     * 测试任务组API
     */
    public function testTaskGroupApi()
    {
        echo "\n=== 测试任务组API ===\n";
        
        // 1. 创建任务组
        echo "1. 创建任务组...\n";
        $createResponse = $this->makeRequest('POST', '/task-groups', [
            'name' => '测试任务组',
            'description' => '这是一个测试任务组',
            'status' => 1
        ], $this->getAuthHeaders());
        
        if ($createResponse['status'] === 201) {
            $taskGroupId = $createResponse['data']['data']['id'];
            echo "✓ 任务组创建成功，ID: $taskGroupId\n";
            
            // 2. 获取任务组列表
            echo "2. 获取任务组列表...\n";
            $listResponse = $this->makeRequest('GET', '/task-groups', null, $this->getAuthHeaders());
            
            if ($listResponse['status'] === 200) {
                $count = count($listResponse['data']['data']['data']);
                echo "✓ 获取任务组列表成功，共 $count 个任务组\n";
            } else {
                echo "✗ 获取任务组列表失败\n";
            }
            
            // 3. 获取任务组详情
            echo "3. 获取任务组详情...\n";
            $showResponse = $this->makeRequest('GET', "/task-groups/$taskGroupId", null, $this->getAuthHeaders());
            
            if ($showResponse['status'] === 200) {
                echo "✓ 获取任务组详情成功\n";
            } else {
                echo "✗ 获取任务组详情失败\n";
            }
            
            // 4. 更新任务组
            echo "4. 更新任务组...\n";
            $updateResponse = $this->makeRequest('PUT', "/task-groups/$taskGroupId", [
                'name' => '更新后的测试任务组',
                'description' => '这是更新后的描述',
                'status' => 1
            ], $this->getAuthHeaders());
            
            if ($updateResponse['status'] === 200) {
                echo "✓ 任务组更新成功\n";
            } else {
                echo "✗ 任务组更新失败\n";
            }
            
            return $taskGroupId;
        } else {
            echo "✗ 任务组创建失败: " . ($createResponse['data']['message'] ?? '未知错误') . "\n";
            return null;
        }
    }
    
    /**
     * 测试监控任务API
     */
    public function testMonitoringTaskApi($taskGroupId = null)
    {
        echo "\n=== 测试监控任务API ===\n";
        
        // 首先需要获取一个可用的数据源ID
        echo "0. 获取数据源列表...\n";
        $dataSourceResponse = $this->makeRequest('GET', '/data-sources', null, $this->getAuthHeaders());
        
        if ($dataSourceResponse['status'] !== 200 || empty($dataSourceResponse['data']['data']['data'])) {
            echo "✗ 没有可用的数据源，请先创建数据源\n";
            return;
        }
        
        $dataSourceId = $dataSourceResponse['data']['data']['data'][0]['id'];
        echo "✓ 找到数据源ID: $dataSourceId\n";
        
        // 1. 创建监控任务
        echo "1. 创建监控任务...\n";
        $taskData = [
            'name' => '测试监控任务',
            'description' => '这是一个测试监控任务',
            'data_source_id' => $dataSourceId,
            'frequency_type' => 'interval',
            'frequency_value' => 60,
            'auto_start' => false,
            'notify_on_error' => true
        ];
        
        if ($taskGroupId) {
            $taskData['task_group_id'] = $taskGroupId;
        }
        
        $createResponse = $this->makeRequest('POST', '/monitoring-tasks', $taskData, $this->getAuthHeaders());
        
        if ($createResponse['status'] === 201) {
            $taskId = $createResponse['data']['data']['id'];
            echo "✓ 监控任务创建成功，ID: $taskId\n";
            
            // 2. 获取监控任务列表
            echo "2. 获取监控任务列表...\n";
            $listResponse = $this->makeRequest('GET', '/monitoring-tasks', null, $this->getAuthHeaders());
            
            if ($listResponse['status'] === 200) {
                $count = count($listResponse['data']['data']['data']);
                echo "✓ 获取监控任务列表成功，共 $count 个任务\n";
            } else {
                echo "✗ 获取监控任务列表失败\n";
            }
            
            // 3. 获取任务状态列表
            echo "3. 获取任务状态列表...\n";
            $statusResponse = $this->makeRequest('GET', '/monitoring-tasks/statuses', null, $this->getAuthHeaders());
            
            if ($statusResponse['status'] === 200) {
                echo "✓ 获取任务状态列表成功\n";
            } else {
                echo "✗ 获取任务状态列表失败\n";
            }
            
            // 4. 获取监控任务详情
            echo "4. 获取监控任务详情...\n";
            $showResponse = $this->makeRequest('GET', "/monitoring-tasks/$taskId", null, $this->getAuthHeaders());
            
            if ($showResponse['status'] === 200) {
                echo "✓ 获取监控任务详情成功\n";
            } else {
                echo "✗ 获取监控任务详情失败\n";
            }
            
            // 5. 更新监控任务
            echo "5. 更新监控任务...\n";
            $updateResponse = $this->makeRequest('PUT', "/monitoring-tasks/$taskId", [
                'name' => '更新后的测试监控任务',
                'description' => '这是更新后的描述',
                'data_source_id' => $dataSourceId,
                'frequency_type' => 'interval',
                'frequency_value' => 120,
                'auto_start' => false,
                'notify_on_error' => true
            ], $this->getAuthHeaders());
            
            if ($updateResponse['status'] === 200) {
                echo "✓ 监控任务更新成功\n";
            } else {
                echo "✗ 监控任务更新失败\n";
            }
            
            // 6. 更新任务状态
            echo "6. 更新任务状态...\n";
            $statusUpdateResponse = $this->makeRequest('PATCH', "/monitoring-tasks/$taskId/status", [
                'status' => 'running'
            ], $this->getAuthHeaders());
            
            if ($statusUpdateResponse['status'] === 200) {
                echo "✓ 任务状态更新成功\n";
            } else {
                echo "✗ 任务状态更新失败\n";
            }
            
            // 7. 获取任务统计
            echo "7. 获取任务统计...\n";
            $statsResponse = $this->makeRequest('GET', "/monitoring-tasks/$taskId/statistics", null, $this->getAuthHeaders());
            
            if ($statsResponse['status'] === 200) {
                echo "✓ 获取任务统计成功\n";
            } else {
                echo "✗ 获取任务统计失败\n";
            }
            
            return $taskId;
        } else {
            echo "✗ 监控任务创建失败: " . ($createResponse['data']['message'] ?? '未知错误') . "\n";
            return null;
        }
    }
    
    /**
     * 清理测试数据
     */
    public function cleanup($taskId = null, $taskGroupId = null)
    {
        echo "\n=== 清理测试数据 ===\n";
        
        if ($taskId) {
            echo "删除测试监控任务...\n";
            $deleteTaskResponse = $this->makeRequest('DELETE', "/monitoring-tasks/$taskId", null, $this->getAuthHeaders());
            
            if ($deleteTaskResponse['status'] === 200) {
                echo "✓ 监控任务删除成功\n";
            } else {
                echo "✗ 监控任务删除失败\n";
            }
        }
        
        if ($taskGroupId) {
            echo "删除测试任务组...\n";
            $deleteGroupResponse = $this->makeRequest('DELETE', "/task-groups/$taskGroupId", null, $this->getAuthHeaders());
            
            if ($deleteGroupResponse['status'] === 200) {
                echo "✓ 任务组删除成功\n";
            } else {
                echo "✗ 任务组删除失败\n";
            }
        }
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests($username, $password)
    {
        echo "开始测试监控任务和任务组API...\n";
        echo "=====================================\n";
        
        // 登录
        if (!$this->login($username, $password)) {
            return;
        }
        
        // 测试任务组API
        $taskGroupId = $this->testTaskGroupApi();
        
        // 测试监控任务API
        $taskId = $this->testMonitoringTaskApi($taskGroupId);
        
        // 清理测试数据
        $this->cleanup($taskId, $taskGroupId);
        
        echo "\n测试完成！\n";
    }
}

// 运行测试
$tester = new MonitoringApiTester($baseUrl);
$tester->runAllTests($testUser['username'], $testUser['password']); 