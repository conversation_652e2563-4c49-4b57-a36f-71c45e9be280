<template>
  <div>
    <h1>创建新的数据源</h1>
    <el-form :model="form" label-width="120px">
      <el-form-item label="数据源名称">
        <el-input v-model="form.name"></el-input>
      </el-form-item>
      <el-form-item label="数据源类型">
        <el-select v-model="form.type" placeholder="请选择类型">
          <el-option label="API" value="api"></el-option>
          <el-option label="网站" value="website"></el-option>
          <el-option label="文件" value="file"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="配置信息">
        <el-input v-model="form.config" type="textarea"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">立即创建</el-button>
        <el-button>取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()

const form = reactive({
  name: '',
  type: '',
  config: ''
})

const onSubmit = async () => {
  try {
    const response = await authStore.request('/api/data-sources', {
      method: 'POST',
      body: JSON.stringify(form)
    })
    
    if (response.success) {
      ElMessage.success('数据源创建成功')
      router.push('/data-sources')
    } else {
      ElMessage.error(response.message || '创建失败')
    }
  } catch (error) {
    console.error('创建数据源失败:', error)
    ElMessage.error('创建数据源失败')
  }
}
</script>

<style scoped>
</style> 